import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DollarSign,
  Star,
  TrendingUp,
  ArrowR<PERSON>,
  Spark<PERSON>,
} from "lucide-react";
import { Loader2, AlertCircle } from "lucide-react";

interface OverviewProps {
  onJoinClick: () => void;
  isLoading?: boolean;
  error?: string | null;
}

const Overview: React.FC<OverviewProps> = ({
  onJoinClick,
  isLoading = false,
  error = null,
}) => {
  return (
    <section className="text-center px-4 relative overflow-hidden bg-gradient-to-b from-background to-background/60">
      {/* Decorative elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-primary/10 rounded-full blur-3xl"></div>

      <div className="relative z-10">
        {/* Header */}
        <div className="mb-2">
          <span className="inline-flex items-center gap-2 text-sm font-medium text-primary mb-3">
            <Sparkles className="h-4 w-4" />
            <span> EARN WITH US</span>
          </span>
        </div>

        <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground inline-block px-8 py-3 rounded-2xl shadow-lg">
          Affiliate Program
        </h1>

        <div className="text-lg md:text-xl max-w-2xl mx-auto mb-12 text-foreground/90 leading-relaxed">
          <p>
            Join our affiliate program and earn commissions by referring new
            customers to our platform. It's easy to get started, and you can
            start earning right away!
          </p>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-14 max-w-5xl mx-auto">
          {[
            {
              icon: DollarSign,
              title: "Earn Money",
              description:
                "Get up to 50% commission on every successful referral",
              gradient: "from-green-500 to-emerald-600",
            },
            {
              icon: Star,
              title: "Premium Support",
              description: "Access to exclusive tools and affiliate support",
              gradient: "from-purple-500 to-indigo-600",
            },
            {
              icon: TrendingUp,
              title: "Growth Opportunities",
              description: "Scale your earnings with performance-based bonuses",
              gradient: "from-amber-500 to-orange-600",
            },
          ].map((feature, index) => (
            <div
              key={index}
              className="bg-card/80 backdrop-blur-sm border border-border p-8 rounded-2xl shadow-sm flex flex-col items-center group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:scale-[1.03]"
            >
              <div
                className={`w-16 h-16 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center mb-5 shadow-md group-hover:shadow-lg transition-all duration-300`}
              >
                <feature.icon className="w-8 h-8 text-white" strokeWidth={2} />
              </div>
              <h3 className="font-bold text-xl mb-3 text-foreground">
                {feature.title}
              </h3>
              <p className="text-md text-center text-foreground/80 mb-4">
                {feature.description}
              </p>
              <div className="mt-auto text-primary flex items-center text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                <span>Learn more</span>
                <ArrowRight className="ml-1 h-4 w-4" />
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-10 bg-card/30 backdrop-blur-sm py-10 px-6 rounded-3xl max-w-3xl mx-auto border border-border/50">
          <h3 className="text-2xl font-bold mb-4">
            Ready to boost your income?
          </h3>
          <p className="mb-6 text-foreground/80 max-w-lg mx-auto">
            Start earning passive income by sharing our platform with your
            audience. No upfront costs, just rewards.
          </p>
          <div className="transition-transform duration-200 hover:scale-[1.03] active:scale-[0.98]">
            <Button
              size="lg"
              className="px-12 py-7 text-lg font-medium rounded-full shadow-md hover:shadow-xl transition-all bg-gradient-to-r from-primary to-primary/80 relative overflow-hidden group"
              onClick={onJoinClick}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                "Get Started"
              )}
            </Button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="w-full max-w-md p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center">
            <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400 mr-2 flex-shrink-0" />
            <span className="text-sm text-red-700 dark:text-red-300">
              {error}
            </span>
          </div>
        )}
      </div>
    </section>
  );
};

export default Overview;
