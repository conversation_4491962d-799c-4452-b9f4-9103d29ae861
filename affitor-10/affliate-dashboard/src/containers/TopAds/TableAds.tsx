import React, { use<PERSON>emo, useState, useEffect } from "react";
import he from "he";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  useReactTable,
  getCoreRowModel,
  ColumnDef,
  flexRender,
} from "@tanstack/react-table";
import {
  EyeIcon,
  Calendar,
  MousePointerClick,
  Share,
  MessageCircle,
  ThumbsUp,
  Store,
  LogIn,
} from "lucide-react";
import { NumberFormat } from "@/components/NumberFormat";
import { IAd } from "@/interfaces";
import _ from "lodash";
import ThumbnailAd from "@/components/ThumbnailAd";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useRouter } from "next/router";
import { useDispatch, useSelector } from "react-redux";
import { actions } from "@/features/top-ads/top-ads.slice";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import Link from "next/link";

export const TableAds: React.FC<{ data: IAd[] }> = ({ data }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const [showAuthPopover, setShowAuthPopover] = useState(false);
  const [authPopoverPosition, setAuthPopoverPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [windowAvailable, setWindowAvailable] = useState(false);

  // Add state for video modal
  const [videoModal, setVideoModal] = useState<{
    isOpen: boolean;
    videoUrl?: string;
    platform?: string;
  }>({
    isOpen: false,
    videoUrl: undefined,
    platform: undefined,
  });

  useEffect(() => {
    // Set window available flag on client-side only
    setWindowAvailable(true);
  }, []);

  // Get loading state from Redux store
  const loadingTranscriptId = useSelector(
    (state: any) => state.topAds.loadingTranscriptId
  );

  // Function to handle AI Script button click
  const handleAIScriptClick = (e: React.MouseEvent, ad: IAd) => {
    e.stopPropagation();

    // Check if user is authenticated
    if (!isAuthenticated) {
      // Only try to access window properties if we're in the browser
      if (windowAvailable) {
        // Use the mouse click position for positioning the popover
        const clickX = e.clientX;
        const clickY = e.clientY;

        // Position the popover near the click point
        setAuthPopoverPosition({
          x: clickX,
          y: clickY + 20, // Position 20px below the click
        });
      }

      // Open auth popover
      setShowAuthPopover(true);
      return;
    }

    if (!ad || !ad.ad_id) {
      console.error("Ad ID not found");
      return;
    }

    // Set current ad first
    dispatch(actions.setCurrentAd(ad));

    // Fetch transcript
    dispatch(actions.fetchAdTranscript(ad.ad_id));
    console.log("Fetching ad transcript...");
  };

  const handleAuthPopoverChange = (open: boolean) => {
    // Only allow opening if user is not authenticated
    // Always allow closing the popover
    if (!open || !isAuthenticated) {
      setShowAuthPopover(open);
    }
  };

  // Function to determine if a specific ad's transcript is loading
  const isThisAdLoading = (adId?: string) => {
    return adId && loadingTranscriptId === adId;
  };

  // Create a function to render the AI Script button consistently
  const renderAIScriptButton = (ad: IAd) => {
    const isLoading = isThisAdLoading(ad.ad_id);

    return (
      <button
        className="inline-flex cursor-pointer whitespace-nowrap items-center justify-center gap-1 px-2 py-1 text-xs font-medium text-purple-600 bg-purple-50 hover:bg-purple-100 rounded-md transition-colors"
        onClick={(e) => handleAIScriptClick(e, ad)}
      >
        {isLoading ? (
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-purple-600 border-t-transparent"></div>
        ) : (
          <svg
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-purple-600"
          >
            <path
              d="M12 1.5L15.09 7.26L21 8.27L16.5 12.14L17.18 18.02L12 15.77L6.82 18.02L7.5 12.14L3 8.27L8.91 7.26L12 1.5Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth="1"
            />
            <circle cx="12" cy="12" r="2" fill="white" />
          </svg>
        )}
        <span>{isLoading ? "Loading..." : "AI Script+"}</span>
      </button>
    );
  };

  // Format date to dd/mm/yyyy
  const formatPublishedDate = (dateString?: string) => {
    if (!dateString) return "";

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "";

      const day = String(date.getDate()).padStart(2, "0");
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    } catch (error) {
      console.error("Error formatting date:", error);
      return "";
    }
  };

  // Function to open video modal
  const handleOpenVideo = (ad: IAd) => {
    let videoUrl = "";

    if (ad.platform === "tiktok" && ad.video_info?.video_url) {
      // Get highest quality available or fallback to any available quality
      videoUrl =
        ad.video_info.video_url["1080p"] ||
        ad.video_info.video_url["720p"] ||
        ad.video_info.video_url["540p"] ||
        ad.video_info.video_url["480p"] ||
        ad.video_info.video_url["360p"] ||
        ad.video_info.video_url["url"] ||
        "";
    } else if (ad.platform === "youtube" && ad.video_info?.video_url) {
      videoUrl = ad.video_info.video_url.embed_url;
    }

    if (videoUrl) {
      setVideoModal({
        isOpen: true,
        videoUrl,
        platform: ad.platform,
      });
    }
  };

  const defColumnsMemo = useMemo<ColumnDef<IAd>[]>(() => {
    return [
      {
        accessorKey: "platform",
        header: "Platform",
        cell: (info: any) => {
          const ad = info.row.original;

          const getPlatformIcon = (platform: string) => {
            switch (platform) {
              case "youtube":
                return (
                  <svg
                    viewBox="0 0 461.001 461.001"
                    className="w-6 h-6"
                    aria-hidden="true"
                  >
                    <g>
                      <path
                        style={{ fill: "#F61C0D" }}
                        d="M365.257,67.393H95.744C42.866,67.393,0,110.259,0,163.137v134.728
                        c0,52.878,42.866,95.744,95.744,95.744h269.513c52.878,0,95.744-42.866,95.744-95.744V163.137
                        C461.001,110.259,418.135,67.393,365.257,67.393z M300.506,237.056l-126.06,60.123c-3.359,1.602-7.239-0.847-7.239-4.568V168.607
                        c0-3.774,3.982-6.22,7.348-4.514l126.06,63.881C304.363,229.873,304.298,235.248,300.506,237.056z"
                      />
                    </g>
                  </svg>
                );
              case "tiktok":
                return (
                  <svg
                    viewBox="0 0 32 32"
                    className="w-6 h-6"
                    aria-hidden="true"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M8.45095 19.7926C8.60723 18.4987 9.1379 17.7743 10.1379 17.0317C11.5688 16.0259 13.3561 16.5948 13.3561 16.5948V13.2197C13.7907 13.2085 14.2254 13.2343 14.6551 13.2966V17.6401C14.6551 17.6401 12.8683 17.0712 11.4375 18.0775C10.438 18.8196 9.90623 19.5446 9.7505 20.8385C9.74562 21.5411 9.87747 22.4595 10.4847 23.2536C10.3345 23.1766 10.1815 23.0889 10.0256 22.9905C8.68807 22.0923 8.44444 20.7449 8.45095 19.7926ZM22.0352 6.97898C21.0509 5.90039 20.6786 4.81139 20.5441 4.04639H21.7823C21.7823 4.04639 21.5354 6.05224 23.3347 8.02482L23.3597 8.05134C22.8747 7.7463 22.43 7.38624 22.0352 6.97898ZM28 10.0369V14.293C28 14.293 26.42 14.2312 25.2507 13.9337C23.6179 13.5176 22.5685 12.8795 22.5685 12.8795C22.5685 12.8795 21.8436 12.4245 21.785 12.3928V21.1817C21.785 21.6711 21.651 22.8932 21.2424 23.9125C20.709 25.246 19.8859 26.1212 19.7345 26.3001C19.7345 26.3001 18.7334 27.4832 16.9672 28.28C15.3752 28.9987 13.9774 28.9805 13.5596 28.9987C13.5596 28.9987 11.1434 29.0944 8.96915 27.6814C8.49898 27.3699 8.06011 27.0172 7.6582 26.6277L7.66906 26.6355C9.84383 28.0485 12.2595 27.9528 12.2595 27.9528C12.6779 27.9346 14.0756 27.9528 15.6671 27.2341C17.4317 26.4374 18.4344 25.2543 18.4344 25.2543C18.5842 25.0754 19.4111 24.2001 19.9423 22.8662C20.3498 21.8474 20.4849 20.6247 20.4849 20.1354V11.3475C20.5435 11.3797 21.2679 11.8347 21.2679 11.8347C21.2679 11.8347 22.3179 12.4734 23.9506 12.8889C25.1204 13.1864 26.7 13.2483 26.7 13.2483V9.91314C27.2404 10.0343 27.7011 10.0671 28 10.0369Z"
                      fill="#EE1D52"
                    />
                    <path
                      d="M26.7009 9.91314V13.2472C26.7009 13.2472 25.1213 13.1853 23.9515 12.8879C22.3188 12.4718 21.2688 11.8337 21.2688 11.8337C21.2688 11.8337 20.5444 11.3787 20.4858 11.3464V20.1364C20.4858 20.6258 20.3518 21.8484 19.9432 22.8672C19.4098 24.2012 18.5867 25.0764 18.4353 25.2553C18.4353 25.2553 17.4337 26.4384 15.668 27.2352C14.0765 27.9539 12.6788 27.9357 12.2604 27.9539C12.2604 27.9539 9.84473 28.0496 7.66995 26.6366L7.6591 26.6288C7.42949 26.4064 7.21336 26.1717 7.01177 25.9257C6.31777 25.0795 5.89237 24.0789 5.78547 23.7934C5.78529 23.7922 5.78529 23.791 5.78547 23.7898C5.61347 23.2937 5.25209 22.1022 5.30147 20.9482C5.38883 18.9122 6.10507 17.6625 6.29444 17.3494C6.79597 16.4957 7.44828 15.7318 8.22233 15.0919C8.90538 14.5396 9.6796 14.1002 10.5132 13.7917C11.4144 13.4295 12.3794 13.2353 13.3565 13.2197V16.5948C13.3565 16.5948 11.5691 16.028 10.1388 17.0317C9.13879 17.7743 8.60812 18.4987 8.45185 19.7926C8.44534 20.7449 8.68897 22.0923 10.0254 22.991C10.1813 23.0898 10.3343 23.1775 10.4845 23.2541C10.7179 23.5576 11.0021 23.8221 11.3255 24.0368C12.631 24.8632 13.7249 24.9209 15.1238 24.3842C16.0565 24.0254 16.7586 23.2167 17.0842 22.3206C17.2888 21.7611 17.2861 21.1978 17.2861 20.6154V4.04639H20.5417C20.6763 4.81139 21.0485 5.90039 22.0328 6.97898C22.4276 7.38624 22.8724 7.7463 23.3573 8.05134C23.5006 8.19955 24.2331 8.93231 25.1734 9.38216C25.6596 9.61469 26.1722 9.79285 26.7009 9.91314Z"
                      fill="#000000"
                    />
                  </svg>
                );
              default:
                return null;
            }
          };

          return (
            <div className="flex items-center justify-center gap-2">
              {getPlatformIcon(ad.platform)}
            </div>
          );
        },
      },
      {
        accessorKey: "thumbnail",
        header: "",
        cell: (info: any) => {
          const ad = info.row.original;

          return (
            <div
              className="w-full min-w-[150px] h-full flex items-center justify-center relative group cursor-pointer"
              onClick={() => handleOpenVideo(ad)}
            >
              <ThumbnailAd
                ad={ad}
                size={{ width: "100%", height: "110px" }}
                className="w-full object-cover transition-opacity group-hover:opacity-80"
              />
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="bg-black bg-opacity-60 rounded-full p-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-white"
                  >
                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                  </svg>
                </div>
              </div>
            </div>
          );
        },
        headerClassName: "w-[150px] truncate",
      },
      {
        accessorKey: "ad_title",
        header: "",
        cell: (info: any) => {
          const ad = info.row.original;
          const title = ad.ad_title || ad.title || "";

          return (
            <div className="flex flex-col gap-1">
              <div className="text-sm font-medium text-foreground line-clamp-2 min-w-[200px] max-w-[300px] overflow-hidden text-ellipsis">
                {he.decode(he.decode(title))}
              </div>
              {/* {ad.createdAt && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                  <Calendar size={12} />
                  <span>{formatPublishedDate(ad.createdAt)}</span>
                </div>
              )} */}
              {ad.brand_name && (
                <div className="mt-1 text-xs text-muted-foreground">
                  Brand: {ad.brand_name}
                </div>
              )}
              <div className="mt-1">
                {ad.platform === "youtube" && renderAIScriptButton(ad)}
              </div>
            </div>
          );
        },
        headerClassName: "w-[200px] truncate",
        cellClassName: "max-w-[200px]",
      },
      {
        accessorKey: "affiliate",
        header: "Affiliate",
        cell: (info: any) => {
          const ad = info.row.original;
          return ad.affiliate?.name ? (
            <a
              className="flex items-center gap-2 cursor-pointer"
              href={`/${ad.affiliate?.industry?.slug || "vertical"}/${
                ad.affiliate?.slug ||
                ad.affiliate?.id ||
                ad.affiliate?.documentId
              }`}
              title={`View ${ad.affiliate.name} details`}
            >
              {ad.affiliate?.image?.url ? (
                <div className="w-8 h-8 overflow-hidden flex-shrink-0">
                  <img
                    src={ad.affiliate.image.url}
                    alt={ad.affiliate.name}
                    className="w-full h-full object-contain"
                  />
                </div>
              ) : (
                <Store size={20} className="text-muted-foreground" />
              )}
              <div className="text-sm text-foreground font-medium hover:underline">
                {ad.affiliate.name}
              </div>
            </a>
          ) : (
            <span className="text-sm text-muted-foreground">-</span>
          );
        },
      },
      // Views column is commented out to hide it
      {
        accessorKey: "views",
        header: "Views",
        cell: (info: any) => (
          <div className="flex items-center gap-1">
            <EyeIcon size={14} className="text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              <NumberFormat value={info.getValue() || 0} />
            </span>
          </div>
        ),
      },
      {
        accessorKey: "engagement",
        header: "Engagement",
        cell: (info: any) => {
          const ad = info.row.original;
          return (
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <ThumbsUp size={14} className="text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    <NumberFormat value={ad.like || 0} />
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <MessageCircle size={14} className="text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    <NumberFormat value={ad.comment || 0} />
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Share size={14} className="text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    <NumberFormat value={ad.share || 0} />
                  </span>
                </div>
              </div>
            </div>
          );
        },
      },
      // Clicks column is commented out to hide it
      /*
      {
        accessorKey: "clicks",
        header: "Clicks",
        cell: (info: any) => (
          <div className="flex items-center gap-1">
            <MousePointerClick size={14} className="text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              <NumberFormat value={info.getValue() || 0} />
            </span>
          </div>
        ),
      },
      */
      {
        accessorKey: "ctr",
        header: "CTR",
        cell: (info: any) => {
          const ad = info.row.original;
          // Use the direct ctr value if available, otherwise calculate it
          const ctrValue =
            ad.ctr !== undefined
              ? ad.ctr
              : ad.views > 0
              ? (ad.clicks / ad.views) * 100
              : 0;

          return (
            <div className="text-sm text-foreground">
              {typeof ctrValue === "number" ? ctrValue.toFixed(2) : "0.00"}%
            </div>
          );
        },
      },
      {
        accessorKey: "cost",
        header: "Cost",
        cell: (info: any) => {
          const cost = info.getValue();
          return (
            <div className="text-sm text-foreground">
              {cost !== undefined ? `$${Number(cost).toFixed(2)}` : "$0.00"}
            </div>
          );
        },
      },
      {
        accessorKey: "country_code",
        header: "Seen In",
        cell: (info: any) => {
          const ad = info.row.original;
          const countries = ad.country_code || ad.seen_in || [];

          return (
            <div className="flex items-center gap-1">
              {countries.length > 0 ? (
                <div className="flex flex-wrap gap-1">
                  {countries.slice(0, 4).map((country: string, idx: number) => (
                    <img
                      key={idx}
                      src={`https://flagcdn.com/w20/${country.toLowerCase()}.png`}
                      alt={country}
                      className="w-5 h-4 object-cover rounded-sm"
                      title={country}
                    />
                  ))}
                  {countries.length > 4 && (
                    <span className="text-xs text-muted-foreground">
                      +{countries.length - 4}
                    </span>
                  )}
                </div>
              ) : (
                <span className="text-xs text-muted-foreground">-</span>
              )}
            </div>
          );
        },
      },
    ];
  }, [router, loadingTranscriptId]);

  const table = useReactTable({
    data,
    columns: defColumnsMemo,
    getCoreRowModel: getCoreRowModel(),
  });

  // Function to render video in modal based on platform
  const renderVideoContent = () => {
    if (!videoModal.videoUrl) return null;

    if (videoModal.platform === "youtube") {
      return (
        <iframe
          src={videoModal.videoUrl}
          className="w-full aspect-video"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        ></iframe>
      );
    } else if (videoModal.platform === "tiktok") {
      return (
        <video
          src={videoModal.videoUrl}
          controls
          autoPlay
          className="max-h-[80vh] max-w-full"
        ></video>
      );
    }

    return null;
  };

  return (
    <>
      <Popover open={showAuthPopover} onOpenChange={handleAuthPopoverChange}>
        <PopoverTrigger className="hidden">
          <span />
        </PopoverTrigger>
        <PopoverContent
          className="w-72 p-4"
          side="bottom"
          align="center"
          style={
            authPopoverPosition && windowAvailable
              ? {
                  position: "fixed",
                  top: `${authPopoverPosition.y}px`,
                  left:
                    Math.max(
                      10,
                      Math.min(
                        typeof window !== "undefined"
                          ? window.innerWidth - 298
                          : 500,
                        authPopoverPosition.x - 144
                      )
                    ) + "px",
                  transform: "none",
                  zIndex: 9999,
                  maxWidth: "calc(100vw - 20px)",
                  width:
                    typeof window !== "undefined" && window.innerWidth <= 298
                      ? `${window.innerWidth - 20}px`
                      : "288px",
                }
              : undefined
          }
        >
          <div className="space-y-3">
            <div className="font-medium text-center flex items-center justify-center gap-2">
              <LogIn size={16} className="text-purple-600" />
              <span>Authentication Required</span>
            </div>
            <p className="text-sm text-muted-foreground">
              You need to be logged in to use the AI script feature. This tool
              analyzes ad content to help you create engaging affiliate content.
            </p>
            <div className="flex justify-center pt-2">
              <Link
                href={`/authentication?redirect=${
                  windowAvailable
                    ? encodeURIComponent(window.location.pathname)
                    : ""
                }`}
                className="inline-flex items-center justify-center rounded-md bg-purple-600 px-4 py-2 text-xs font-medium text-white hover:bg-purple-700"
              >
                Login to continue
              </Link>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      <Table className="min-w-full">
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow key={row.id}>
              {row.getVisibleCells().map((cell: any) => (
                <TableCell key={cell.id} className="p-4">
                  {cell.column.columnDef.cell(cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Video Modal */}
      <Dialog
        open={videoModal.isOpen}
        onOpenChange={(open) =>
          setVideoModal((prev) => ({ ...prev, isOpen: open }))
        }
      >
        <DialogContent className="sm:max-w-[400px] p-0 overflow-hidden">
          {renderVideoContent()}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TableAds;
