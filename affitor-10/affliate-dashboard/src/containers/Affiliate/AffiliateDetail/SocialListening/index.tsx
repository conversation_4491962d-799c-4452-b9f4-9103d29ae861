import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { actions } from "@/features/social-listening/social-listening.slice";
import {
  selectSocialListeningList,
  selectCurrentAffiliate,
  selectSocialListeningLoading,
  selectSocialListeningPagination,
} from "@/features/selectors";
import { CircularProgress } from "@mui/material";
import { FilterSelector } from "./FilterSelector";
import PostCard from "./PostCard";
import { ChevronDown, ChevronUp } from "lucide-react";

function SocialListening() {
  const dispatch = useDispatch();
  const socialListenings = useSelector(selectSocialListeningList);
  const currentAffiliate = useSelector(selectCurrentAffiliate);
  const loading = useSelector(selectSocialListeningLoading);
  const socialPagination = useSelector(selectSocialListeningPagination);

  const TAB_OPTIONS = [
    { name: "All", id: "youtube,tiktok" },
    { name: "Youtube", id: "youtube" },
    { name: "Tik<PERSON>", id: "tiktok" }
  ];

  const SORT_OPTIONS = [
    { value: "views", name: "Views" },
    { value: "likes", name: "Likes" },
    { value: "comments", name: "Comments" },
    { value: "published_from", name: "Date" },
  ];

  const [selectedTab, setSelectedTab] = useState(TAB_OPTIONS[0]);
  const [currentPage, setCurrentPage] = useState(1);
  const [sort, setSort] = useState(SORT_OPTIONS[0].value);

  useEffect(() => {
    dispatch(actions.setSocialListenings(null));
  }, []);

  useEffect(() => {
    if (currentAffiliate?.documentId) {
      dispatch(
        actions.fetch({
          platforms: selectedTab?.id?.split(","),
          affiliateDocId: currentAffiliate?.documentId,
          pagination: { page: currentPage, pageSize: 5 }, 
          sort: { field: sort, order: "desc" },
        })
      );
    }
  }, [currentAffiliate, selectedTab, sort, currentPage]);

  const handleShowMoreClick = useCallback(() => {
    setCurrentPage((prev) => prev + 1);
  }, [setCurrentPage]);

  const handleShowLessClick = useCallback(() => {
    setCurrentPage(1);
    dispatch(actions.setSocialListenings(null));
  }, [dispatch]);

  const handleSelectTab = useCallback(
    (tab: { name: string; id: string }) => {
      setSelectedTab(tab);
      setCurrentPage(1);
      dispatch(actions.setSocialListenings(null));
    },
    [setSelectedTab, setCurrentPage, dispatch]
  );

  const showShowMoreButton = Array.isArray(socialListenings) && socialListenings.length > 0 && !loading && socialPagination?.page !== socialPagination?.pageCount;
  const showShowLessButton = Array.isArray(socialListenings) && socialListenings.length > 9 && !loading;

  return (
    <div className="p-4 bg-primary shadow rounded-lg text-xs md:text-base">
      <h1 className="text-lg md:text-xl font-bold mb-6 text-primary-foreground">Social Listening</h1>
      
      <div className="flex gap-2 mb-6 overflow-x-auto">
        {TAB_OPTIONS.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleSelectTab(tab)}
            className={`px-4 md:px-6 py-1 md:py-2 rounded-full text-xs md:text-sm font-medium cursor-pointer border ${
              selectedTab.id === tab.id
                ? "bg-blue-600 text-white border-blue-600"
                : "bg-transparent border-blue-500 text-blue-500 hover:bg-blue-500/10 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-400/10"
            }`}
          >
            {tab.name}
          </button>
        ))}
      </div>
      
      <div className="flex justify-start mb-6 items-center gap-2">
        <div className="flex items-center gap-2">
          <span className="text-xs md:text-sm font-medium text-primary-foreground">Sort:</span>
          <FilterSelector
            title=""
            options={SORT_OPTIONS}
            selected={sort}
            onChange={(value) => {
              setSort(value);
              dispatch(actions.setSocialListenings(null));
              setCurrentPage(1);
            }}
          />
        </div>
      </div>
      
      {/* Table Header */}
      <div className="hidden md:flex w-full items-center gap-4 py-2 text-xs font-semibold text-slate-500 dark:text-slate-200">
        <div className="flex-shrink-0 w-28 text-center">PLATFORM</div>
        <div className="flex-1 min-w-0">TITLE</div>
        <div className="flex flex-shrink-0 items-center gap-4 text-xs">
          <div className="w-16 text-center">VIEWS</div>
          <div className="w-16 text-center">LIKES</div>
          <div className="w-16 text-center">COMMENTS</div>
          <div className="w-20 text-center">DATE</div>
          <div className="w-8 text-center">LINK</div>
        </div>
      </div>

      {/* Scrollable container for all PostCards */}
      <div className="relative">
        {/* Table container with controlled overflow */}
        <div className="overflow-x-auto md:overflow-visible hide-scrollbar  ">
            {socialListenings === null ? (
            <div className="text-center py-8">
              <CircularProgress color="inherit" />
            </div>
            ) : (
            <div className="w-full md:min-w-0 divide-y divide-slate-200 dark:divide-slate-800">
              {socialListenings?.length === 0 ? (
              <p>No data available</p>
              ) : (
              socialListenings.map((post, index) => (
                <PostCard key={index} post={post} />
              ))
              )}
            </div>
            )}
            
        </div>
      </div>
      
      {(showShowMoreButton || showShowLessButton) && (
        <div className="flex justify-center items-center gap-4 mt-6">
          {showShowMoreButton && (
            <button
              onClick={handleShowMoreClick}
              className="inline-flex items-center gap-1 rounded-full border border-slate-300 dark:border-slate-700 bg-transparent px-3 py-1 text-xs font-medium text-slate-600 dark:text-slate-300 transition-colors hover:bg-slate-100 dark:hover:bg-slate-800"
            >
              <span>Show More</span>
              <ChevronDown size={14} />
            </button>
          )}

          {showShowLessButton && (
            <button
              onClick={handleShowLessClick}
              className="inline-flex items-center gap-1 rounded-full border border-slate-300 dark:border-slate-700 bg-transparent px-3 py-1 text-xs font-medium text-slate-600 dark:text-slate-300 transition-colors hover:bg-slate-100 dark:hover:bg-slate-800"
            >
              <span>Show Less</span>
              <ChevronUp size={14} />
            </button>
          )}
        </div>
      )}
      
      {loading && currentPage > 1 && (
        <div className="flex justify-center mt-6">
          <CircularProgress color="inherit" size={24} />
        </div>
      )}
    </div>
  );
}

export default SocialListening;