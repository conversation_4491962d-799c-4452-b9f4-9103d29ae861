import { useState } from "react";
import { useTheme } from "next-themes";
import {
  Popover,
  Popover<PERSON><PERSON>ger,
  PopoverContent,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import {
  BarChart2,
  Heart,
  MessageSquare,
  Calendar,
  ChevronDown,
} from "lucide-react";

const getIconForOption = (value: string) => {
  switch (value) {
    case "views":
      return <BarChart2 size={14} />;
    case "likes":
      return <Heart size={14} />;
    case "comments":
      return <MessageSquare size={14} />;
    case "published_from":
      return <Calendar size={14} />;
    default:
      return <BarChart2 size={14} />;
  }
};

export function FilterSelector({
  options,
  title,
  onChange,
  selected,
}: {
  options: { name: string; icon?: string; value: string }[];
  title: string;
  selected?: string;
  onChange: (value: string) => void;
}) {
  const { theme } = useTheme();
  const [open, setOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(
    selected || options[0]?.value || ""
  );

  const selectedOption = options.find(
    (opt) => opt.value === (selected || selectedValue)
  );

  const handleSelect = (value: string) => {
    setSelectedValue(value);
    onChange(value);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-1 rounded-full px-3 py-1 text-xs h-auto"
        >
          {title && <span className="font-medium">{title}:</span>}
          {getIconForOption(selectedOption?.value || "")}
          <span>{selectedOption?.name || ""}</span>
          <ChevronDown size={14} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-40 p-1">
        <div className="grid gap-1">
          {options.map((option) => (
            <Button
              key={option.value}
              variant={
                option.value === (selected || selectedValue) ? "secondary" : "ghost"
              }
              onClick={() => handleSelect(option.value)}
              className="flex items-center justify-start gap-2 w-full h-auto py-1.5 px-2 text-xs"
            >
              {getIconForOption(option.value)}
              <span>{option.name}</span>
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}