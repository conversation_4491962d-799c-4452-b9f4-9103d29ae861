import { ProgramDetail, ProgramQuickInfo, Loading } from "@/components";
import AffiliateHeader from "@/containers/Affiliate/AffiliateDetail/AffiliateHeader";
import SimilarPrograms from "@/containers/Affiliate/AffiliateDetail/SimilarPrograms";
import SocialListening from "@/containers/Affiliate/AffiliateDetail/SocialListening";
import SummaryData from "@/containers/Affiliate/AffiliateDetail/SummaryData";
import { affiliateActions, trafficWebActions } from "@/features/rootActions";
import { selectCurrentAffiliate } from "@/features/selectors";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import VisitOverTime from "./VisitOverTime";
import TopKeywords from "./TopKeywords";
import Geography from "./Geography";
import TrafficSources from "./TrafficSources";
import { selectTrafficWebList } from "@/features/traffic-web/traffic-web.slice";
import { ChevronDown, ChevronUp } from "lucide-react";

export default function AffiliateDetail() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { id } = router.query;
  const program = useSelector(selectCurrentAffiliate);
  const { fetchDetail, setCurrentAffiliate } = affiliateActions;
  const currentAffiliate = useSelector(selectCurrentAffiliate);
  const trafficWebs = useSelector(selectTrafficWebList);
  const [showAdvanced, setShowAdvanced] = useState(false);

  useEffect(() => {
    if (!id || typeof id !== "string") return;
    dispatch(setCurrentAffiliate(null));
    dispatch(fetchDetail({ id: id as string }));
  }, [id]);

  useEffect(() => {
    if (currentAffiliate?.documentId && router.query.id) {
      dispatch(affiliateActions.setAffiliates(null));
      dispatch(trafficWebActions.setTrafficWebs(null));
      dispatch(
        trafficWebActions.fetch({
          affiliateDocId: currentAffiliate.documentId,
        })
      );
    }
  }, [currentAffiliate, router.query.id]);

  return (
    <div className="bg-secondary p-0 md:p-5">
      <div className="space-y-4">
        {program ? (
          <div className="space-y-4 mx-auto max-w-5xl">
            <div className="p-4 md:p-6 bg-primary shadow rounded-lg space-y-6">
              <AffiliateHeader program={program} />
              <SummaryData
                commission={program.commission?.title}
                currency={program.currency}
                monthlyTraffic={
                  trafficWebs?.[0]?.visits || program.monthly_traffic
                }
                avg_conversion={program.avg_conversion}
                paymentMethods={program.payment_methods}
                minimumPayout={program.minimum_payout}
                cookieDuration={program.cookies_duration}
              />
               <div className="flex flex-col lg:flex-row gap-5">
                <div className="lg:flex-7 w-full lg:w-auto">
                  <ProgramDetail program={program} />
                </div>
              </div>
            </div>

            {/* Only render SocialListening if monthly traffic is at least 60k */}
            {(program.monthly_traffic) >= 60000 && (
              <SocialListening />
            )}
            
            <div className="p-4 bg-primary shadow rounded-lg">
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-xl md:text-2xl font-bold text-primary-foreground">Analytics</h1>
              </div>

              <div className="space-y-4">
                <div>
                  <h2 className="text-base md:text-lg font-bold mb-4 text-primary-foreground">Visits Over Time</h2>
                  <VisitOverTime />
                </div>

                {showAdvanced && (
                  <div className="space-y-4 pt-4 border-t border-border">
                    <div className="flex flex-col lg:flex-row gap-4">
                      <div className="lg:w-1/2">
                        <TopKeywords />
                      </div>
                      <div className="lg:w-1/2">
                        <Geography />
                      </div>
                    </div>
                    <TrafficSources />
                  </div>
                )}

                <div className="flex-1 w-full lg:w-auto">
                  <div className="text-center">
                    <button
                      onClick={() => setShowAdvanced(!showAdvanced)}
                      className="inline-flex items-center gap-1 rounded-full border border-slate-300 dark:border-slate-700 bg-transparent px-3 py-1 text-xs font-medium text-slate-600 dark:text-slate-300 transition-colors hover:bg-slate-100 dark:hover:bg-slate-800"
                    >
                      <span>
                        {showAdvanced
                          ? "Hide Advanced Analytics"
                          : "Show Advanced Analytics"}
                      </span>
                      {showAdvanced ? (
                        <ChevronUp size={14} />
                      ) : (
                        <ChevronDown size={14} />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <SimilarPrograms />
          </div>
        ) : (
          <Loading />
        )}
      </div>
    </div>
  );
}
