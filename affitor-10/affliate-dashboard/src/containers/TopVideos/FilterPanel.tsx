import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useDispatch, useSelector } from "react-redux";
import { RotateCcw, X, Filter, Check } from "lucide-react";
import { selectCategories } from "@/features/selectors";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Custom hook for responsive design
const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const media = window.matchMedia(query);
      if (media.matches !== matches) {
        setMatches(media.matches);
      }
      
      const listener = () => setMatches(media.matches);
      media.addEventListener("change", listener);
      return () => media.removeEventListener("change", listener);
    }
    return () => {};
  }, [matches, query]);

  return matches;
};

// Stable input hook for better form control
const useStableInput = (
  initialValue: string,
  onChange: (value: string) => void
) => {
  const [value, setValue] = useState(initialValue);
  
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
  }, []);

  const handleBlur = useCallback(() => {
    onChange(value);
  }, [value, onChange]);

  return {
    value,
    onChange: handleChange,
    onBlur: handleBlur,
  };
};

// Format number with commas
const formatNumberWithCommas = (value: string): string => {
  if (!value) return '';
  
  // Remove any non-digit characters
  const numericValue = value.replace(/[^0-9]/g, '');
  
  // Format with commas
  return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// Parse formatted number back to raw value
const parseFormattedNumber = (formatted: string): string => {
  return formatted.replace(/,/g, '');
};

// Reusable input component
const StableInput = ({
  value,
  onChange,
  placeholder = "Min",
  unit,
  className = "",
  formatNumber = false,
  ...props
}: {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  unit?: string;
  className?: string;
  formatNumber?: boolean;
  [key: string]: any;
}) => {
  // Internal state to handle display value
  const [internalValue, setInternalValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  
  // Update internal value when external value changes, but only when not focused
  useEffect(() => {
    if (!isFocused) {
      setInternalValue(formatNumber ? formatNumberWithCommas(value) : value);
    }
  }, [value, formatNumber, isFocused]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // Allow only digits and commas for number format
    if (formatNumber && !/^[0-9,]*$/.test(inputValue)) {
      return;
    }
    
    // Update internal value immediately for smooth typing experience
    setInternalValue(inputValue);
  };

  const handleBlur = () => {
    setIsFocused(false);
    // On blur, format the value and pass the raw value to parent
    const formattedValue = formatNumber ? formatNumberWithCommas(internalValue) : internalValue;
    setInternalValue(formattedValue);
    
    // Only pass raw numeric value to parent
    const rawValue = formatNumber ? parseFormattedNumber(internalValue) : internalValue;
    onChange(rawValue);
  };
  
  const handleFocus = () => {
    setIsFocused(true);
  };

  return (
    <div className="flex-1 relative">
      <input
        type="text"
        inputMode={formatNumber ? "numeric" : "text"}
        className={`w-full border border-input bg-background rounded-md px-3 py-1 ${
          unit ? "pr-12" : "pr-3"
        } h-8 text-foreground shadow-none outline-none focus:outline-none focus:border-primary focus:ring-0 text-[16px] md:text-sm ${className}`}
        placeholder={placeholder}
        value={internalValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        style={{
          fontSize: '16px',
          transformOrigin: 'left center',
        }}
        {...props}
      />
      {unit && (
        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground text-xs">
          {unit}
        </span>
      )}
    </div>
  );
};

// Define filter state interface
export interface VideoFilterState {
  platforms: string[];
  categories: string[];
  minViews: string;
}

interface FilterPanelProps {
  filters: VideoFilterState;
  onFilterChange: (filters: VideoFilterState) => void;
}

export default function FilterPanel({ filters, onFilterChange }: FilterPanelProps) {
  const dispatch = useDispatch();
  const [localFilters, setLocalFilters] = useState<VideoFilterState>(filters);
  const categories = useSelector(selectCategories);
  
  // UI state
  const [isOpen, setIsOpen] = useState(false);
  const [wasReset, setWasReset] = useState(false);
  
  // Store original filters to track changes
  const originalFiltersRef = useRef(filters);
  
  // Calculate active filter count
  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (localFilters.platforms.length > 0) count++;
    if (localFilters.categories.length > 0) count++;
    if (localFilters.minViews) count++;
    return count;
  }, [localFilters]);

  // Ensure local filters are always in sync with props
  useEffect(() => {
    setLocalFilters(filters);
    // Also update the original filters ref when filters change from outside
    if (!isOpen) {
      originalFiltersRef.current = { ...filters };
    }
  }, [filters, isOpen]);

  // Store original filters when opening the panel
  useEffect(() => {
    if (isOpen) {
      originalFiltersRef.current = { ...filters };
      setWasReset(false);
    } else if (wasReset) {
      setLocalFilters({ ...originalFiltersRef.current });
      setWasReset(false);
    }
  }, [isOpen, wasReset, filters]);

  // Input change handlers
  const handleInputChange = useCallback(
    (field: keyof VideoFilterState, value: string) => {
      // Only allow numbers
      const numericValue = value.replace(/[^0-9]/g, "");
      
      setLocalFilters((prev) => ({
        ...prev,
        [field]: numericValue,
      }));
    },
    []
  );

  // Platform selection handler
  const handlePlatformChange = useCallback((value: string) => {
    setLocalFilters(prev => {
      // Toggle platform selection
      const currentPlatforms = [...prev.platforms];
      if (currentPlatforms.includes(value)) {
        return {
          ...prev,
          platforms: currentPlatforms.filter(p => p !== value)
        };
      } else {
        return {
          ...prev,
          platforms: [...currentPlatforms, value]
        };
      }
    });
  }, []);

  // Category selection handler
  const handleCategoryChange = useCallback((categoryId: string, isChecked: boolean) => {
    setLocalFilters(prev => {
      const currentCategories = [...prev.categories];
      
      if (isChecked && !currentCategories.includes(categoryId)) {
        return {
          ...prev,
          categories: [...currentCategories, categoryId]
        };
      } else if (!isChecked && currentCategories.includes(categoryId)) {
        return {
          ...prev,
          categories: currentCategories.filter(id => id !== categoryId)
        };
      }
      
      return prev;
    });
  }, []);

  // Clear filter handlers
  const handleClearPlatforms = useCallback(() => {
    setLocalFilters(prev => ({
      ...prev,
      platforms: []
    }));
  }, []);

  const handleClearCategories = useCallback(() => {
    setLocalFilters(prev => ({
      ...prev,
      categories: []
    }));
  }, []);

  const handleClearRevenue = useCallback(() => {
    setLocalFilters(prev => ({
      ...prev,
      minViews: ""
    }));
  }, []);

  // Apply and reset handlers
  const handleApplyFilter = useCallback(() => {
    const isFilterChanged = JSON.stringify(localFilters) !== JSON.stringify(originalFiltersRef.current);
    
    if (isFilterChanged) {
      onFilterChange(localFilters);
    }
    
    setIsOpen(false);
  }, [localFilters, onFilterChange]);

  const handleResetFilters = useCallback(() => {
    setLocalFilters({
      platforms: [],
      categories: [],
      minViews: ""
    });
    setWasReset(true);
  }, []);

  // Handle open/close states
  const handleOpenChange = useCallback(
    (open: boolean) => {
      if (!open && wasReset) {
        setLocalFilters(originalFiltersRef.current);
        setWasReset(false);
      }
      setIsOpen(open);
    },
    [wasReset]
  );

  // Trigger button
  const triggerButton = useMemo(
    () => (
      <button className="flex items-center gap-2 px-3 py-2 rounded-lg bg-secondary hover:bg-primary hover:text-primary-foreground transition-colors shadow-sm text-[14px] text-secondary-foreground">
        <div className="relative">
          <Filter size={16} />
          {activeFilterCount > 0 && (
            <span className="absolute -top-2 -right-2 flex items-center justify-center w-4 h-4 bg-primary text-primary-foreground text-[10px] font-bold rounded-full">
              {activeFilterCount}
            </span>
          )}
        </div>
        <span className="hidden sm:inline">Filter</span>
      </button>
    ),
    [activeFilterCount]
  );

  // Filter content component
  const FilterContent = useCallback(
    () => {
      const isFilterChanged = JSON.stringify(localFilters) !== JSON.stringify(originalFiltersRef.current);
      
      return (
        <div className="grid grid-cols-1 gap-3">
          {/* Platform Filter */}
          <div className="mb-1">
            <div className="flex flex-row gap-2 items-start">
              <label className="text-sm font-medium text-foreground w-1/3 pt-1.5">
                Platforms
              </label>
              <div className="relative w-2/3">
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handlePlatformChange("youtube")}
                    className={`px-4 py-1.5 rounded-md text-xs font-medium transition-all ${
                      localFilters.platforms.includes("youtube")
                        ? "bg-blue-500 text-primary border border-border"
                        : "bg-secondary text-secondary-foreground border border-border hover:bg-secondary/80"
                    }`}
                  >
                    <div className="flex items-center gap-1.5">
                      <svg
                        viewBox="0 0 461.001 461.001"
                        className="w-4 h-4"
                        aria-hidden="true"
                      >
                        <g>
                          <path
                            style={{
                              fill: localFilters.platforms.includes("youtube")
                                ? "white"
                                : "#F61C0D",
                            }}
                            d="M365.257,67.393H95.744C42.866,67.393,0,110.259,0,163.137v134.728
                            c0,52.878,42.866,95.744,95.744,95.744h269.513c52.878,0,95.744-42.866,95.744-95.744V163.137
                            C461.001,110.259,418.135,67.393,365.257,67.393z M300.506,237.056l-126.06,60.123c-3.359,1.602-7.239-0.847-7.239-4.568V168.607
                            c0-3.774,3.982-6.22,7.348-4.514l126.06,63.881C304.363,229.873,304.298,235.248,300.506,237.056z"
                          />
                        </g>
                      </svg>
                      YouTube
                    </div>
                  </button>
                  <button
                    onClick={() => handlePlatformChange("tiktok")}
                    className={`px-4 py-1.5 rounded-md text-xs font-medium transition-all ${
                      localFilters.platforms.includes("tiktok")
                        ? "bg-blue-500 text-primary border border-border"
                        : "bg-secondary text-secondary-foreground border border-border hover:bg-secondary/80"
                    }`}
                  >
                    <div className="flex items-center gap-1.5">
                      <svg
                        viewBox="0 0 32 32"
                        className="w-4 h-4"
                        aria-hidden="true"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8.45095 19.7926C8.60723 18.4987 9.1379 17.7743 10.1379 17.0317C11.5688 16.0259 13.3561 16.5948 13.3561 16.5948V13.2197C13.7907 13.2085 14.2254 13.2343 14.6551 13.2966V17.6401C14.6551 17.6401 12.8683 17.0712 11.4375 18.0775C10.438 18.8196 9.90623 19.5446 9.7505 20.8385C9.74562 21.5411 9.87747 22.4595 10.4847 23.2536C10.3345 23.1766 10.1815 23.0889 10.0256 22.9905C8.68807 22.0923 8.44444 20.7449 8.45095 19.7926ZM22.0352 6.97898C21.0509 5.90039 20.6786 4.81139 20.5441 4.04639H21.7823C21.7823 4.04639 21.5354 6.05224 23.3347 8.02482L23.3597 8.05134C22.8747 7.7463 22.43 7.38624 22.0352 6.97898ZM28 10.0369V14.293C28 14.293 26.42 14.2312 25.2507 13.9337C23.6179 13.5176 22.5685 12.8795 22.5685 12.8795C22.5685 12.8795 21.8436 12.4245 21.785 12.3928V21.1817C21.785 21.6711 21.651 22.8932 21.2424 23.9125C20.709 25.246 19.8859 26.1212 19.7345 26.3001C19.7345 26.3001 18.7334 27.4832 16.9672 28.28C15.3752 28.9987 13.9774 28.9805 13.5596 28.9987C13.5596 28.9987 11.1434 29.0944 8.96915 27.6814C8.49898 27.3699 8.06011 27.0172 7.6582 26.6277L7.66906 26.6355C9.84383 28.0485 12.2595 27.9528 12.2595 27.9528C12.6779 27.9346 14.0756 27.9528 15.6671 27.2341C17.4317 26.4374 18.4344 25.2543 18.4344 25.2543C18.5842 25.0754 19.4111 24.2001 19.9423 22.8662C20.3498 21.8474 20.4849 20.6247 20.4849 20.1354V11.3475C20.5435 11.3797 21.2679 11.8347 21.2679 11.8347C21.2679 11.8347 22.3179 12.4734 23.9506 12.8889C25.1204 13.1864 26.7 13.2483 26.7 13.2483V9.91314C27.2404 10.0343 27.7011 10.0671 28 10.0369Z"
                          fill={
                            localFilters.platforms.includes("tiktok")
                              ? "white"
                              : "#EE1D52"
                          }
                        />
                        <path
                          d="M26.7009 9.91314V13.2472C26.7009 13.2472 25.1213 13.1853 23.9515 12.8879C22.3188 12.4718 21.2688 11.8337 21.2688 11.8337C21.2688 11.8337 20.5444 11.3787 20.4858 11.3464V20.1364C20.4858 20.6258 20.3518 21.8484 19.9432 22.8672C19.4098 24.2012 18.5867 25.0764 18.4353 25.2553C18.4353 25.2553 17.4337 26.4384 15.668 27.2352C14.0765 27.9539 12.6788 27.9357 12.2604 27.9539C12.2604 27.9539 9.84473 28.0496 7.66995 26.6366L7.6591 26.6288C7.42949 26.4064 7.21336 26.1717 7.01177 25.9257C6.31777 25.0795 5.89237 24.0789 5.78547 23.7934C5.78529 23.7922 5.78529 23.791 5.78547 23.7898C5.61347 23.2937 5.25209 22.1022 5.30147 20.9482C5.38883 18.9122 6.10507 17.6625 6.29444 17.3494C6.79597 16.4957 7.44828 15.7318 8.22233 15.0919C8.90538 14.5396 9.6796 14.1002 10.5132 13.7917C11.4144 13.4295 12.3794 13.2353 13.3565 13.2197V16.5948C13.3565 16.5948 11.5691 16.028 10.1388 17.0317C9.13879 17.7743 8.60812 18.4987 8.45185 19.7926C8.44534 20.7449 8.68897 22.0923 10.0254 22.991C10.1813 23.0898 10.3343 23.1775 10.4845 23.2541C10.7179 23.5576 11.0021 23.8221 11.3255 24.0368C12.631 24.8632 13.7249 24.9209 15.1238 24.3842C16.0565 24.0254 16.7586 23.2167 17.0842 22.3206C17.2888 21.7611 17.2861 21.1978 17.2861 20.6154V4.04639H20.5417C20.6763 4.81139 21.0485 5.90039 22.0328 6.97898C22.4276 7.38624 22.8724 7.7463 23.3573 8.05134C23.5006 8.19955 24.2331 8.93231 25.1734 9.38216C25.6596 9.61469 26.1722 9.79285 26.7009 9.91314Z"
                          fill={
                            localFilters.platforms.includes("tiktok")
                              ? "white"
                              : "#000000"
                          }
                        />
                        <path
                          d="M4.48926 22.7568V22.7594L4.57004 22.9784C4.56076 22.9529 4.53074 22.8754 4.48926 22.7568Z"
                          fill={
                            localFilters.platforms.includes("tiktok")
                              ? "white"
                              : "#69C9D0"
                          }
                        />
                        <path
                          d="M10.5128 13.7916C9.67919 14.1002 8.90498 14.5396 8.22192 15.0918C7.44763 15.7332 6.79548 16.4987 6.29458 17.354C6.10521 17.6661 5.38897 18.9168 5.30161 20.9528C5.25223 22.1068 5.61361 23.2983 5.78561 23.7944C5.78543 23.7956 5.78543 23.7968 5.78561 23.798C5.89413 24.081 6.31791 25.0815 7.01191 25.9303C7.2135 26.1763 7.42963 26.4111 7.65924 26.6334C6.92357 26.1457 6.26746 25.5562 5.71236 24.8839C5.02433 24.0451 4.60001 23.0549 4.48932 22.7626C4.48919 22.7605 4.48919 22.7584 4.48932 22.7564V22.7527C4.31677 22.2571 3.95431 21.0651 4.00477 19.9096C4.09213 17.8736 4.80838 16.6239 4.99775 16.3108C5.4985 15.4553 6.15067 14.6898 6.92509 14.0486C7.608 13.4961 8.38225 13.0567 9.21598 12.7484C9.73602 12.5416 10.2778 12.3891 10.8319 12.2934C11.6669 12.1537 12.5198 12.1415 13.3588 12.2575V13.2196C12.3808 13.2349 11.4148 13.4291 10.5128 13.7916Z"
                          fill={
                            localFilters.platforms.includes("tiktok")
                              ? "white"
                              : "#69C9D0"
                          }
                        />
                        <path
                          d="M20.5438 4.04635H17.2881V20.6159C17.2881 21.1983 17.2881 21.76 17.0863 22.3211C16.7575 23.2167 16.058 24.0253 15.1258 24.3842C13.7265 24.923 12.6326 24.8632 11.3276 24.0368C11.0036 23.823 10.7187 23.5594 10.4844 23.2567C11.5962 23.8251 12.5913 23.8152 13.8241 23.341C14.7558 22.9821 15.4563 22.1734 15.784 21.2774C15.9891 20.7178 15.9864 20.1546 15.9864 19.5726V3H20.4819C20.4819 3 20.4315 3.41188 20.5438 4.04635ZM26.7002 8.99104V9.9131C26.1725 9.79263 25.6609 9.61447 25.1755 9.38213C24.2352 8.93228 23.5026 8.19952 23.3594 8.0513C23.5256 8.1559 23.6981 8.25106 23.8759 8.33629C25.0192 8.88339 26.1451 9.04669 26.7002 8.99104Z"
                          fill={
                            localFilters.platforms.includes("tiktok")
                              ? "white"
                              : "#69C9D0"
                          }
                        />
                      </svg>
                      TikTok
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Categories Filter */}
          <div className="mb-1">
            <div className="flex flex-row gap-2 items-start">
              <label className="text-sm font-medium text-foreground w-1/3 pt-1.5">
                Categories
              </label>
              <div className="relative w-2/3">
                <div className="max-h-[180px] overflow-y-auto pr-2 space-y-1.5 pt-1">
                  {categories && categories.length > 0 ? (
                    categories.map((category) => {
                      // Convert category.id to string for comparison since localFilters.categories contains strings
                      const isChecked = localFilters.categories.includes(
                        String(category.id)
                      );
                      return (
                        <div
                          key={category.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`category-${category.id}`}
                            checked={isChecked}
                            onCheckedChange={(checked) =>
                              handleCategoryChange(
                                String(category.id),
                                checked === true
                              )
                            }
                            disabled={false}
                            className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                          />
                          <label
                            htmlFor={`category-${category.id}`}
                            className={`text-sm ${
                              isChecked
                                ? "text-foreground font-medium"
                                : "text-muted-foreground"
                            } cursor-pointer hover:text-foreground`}
                          >
                            {category.name}
                          </label>
                        </div>
                      );
                    })
                  ) : (
                    <p className="text-xs text-muted-foreground italic">
                      No categories available
                    </p>
                  )}
                </div>
                {localFilters.categories.length > 0 && (
                  <button
                    onClick={handleClearCategories}
                    className="absolute right-[-8px] top-[-8px] flex items-center justify-center h-5 w-5 rounded-full bg-secondary hover:bg-secondary/80 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Min Views */}
          <div className="mb-1">
            <div className="flex flex-row gap-2 items-center">
              <label className="text-sm font-medium text-foreground w-1/3">
                Min Views
              </label>
              <div className="relative w-2/3">
                <StableInput
                  value={localFilters.minViews}
                  onChange={(value) => handleInputChange("minViews", value)}
                  placeholder="Minimum views"
                  formatNumber={true}
                />
                {localFilters.minViews && (
                  <button
                    onClick={handleClearRevenue}
                    className="absolute right-8 top-1/2 -translate-y-1/2 flex items-center justify-center h-5 w-5 rounded-full bg-secondary hover:bg-secondary/80 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between gap-4 mt-4">
            <button
              className="text-blue-800 py-1 px-3 h-8 rounded-md hover:text-blue-900 transition flex items-center justify-center gap-1 bg-transparent cursor-pointer text-sm"
              onClick={handleResetFilters}
            >
              <RotateCcw className="h-3 w-3" />
              <span>Reset</span>
            </button>
            <button
              className={`py-1 px-4 h-8 rounded-md transition flex items-center justify-center text-sm ${
                isFilterChanged
                  ? "bg-blue-600 text-white hover:bg-blue-900 cursor-pointer"
                  : "bg-gray-400 text-gray-100 cursor-not-allowed"
              }`}
              onClick={handleApplyFilter}
              disabled={!isFilterChanged}
            >
              Apply Filters
            </button>
          </div>
        </div>
      );
    },
    [
      localFilters,
      categories,
      handleInputChange,
      handlePlatformChange,
      handleCategoryChange,
      handleClearPlatforms,
      handleClearCategories,
      handleClearRevenue,
      handleResetFilters,
      handleApplyFilter,
    ]
  );

  // Check if on mobile
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Render dialog on mobile, popover on desktop
  if (isMobile) {
    return (
      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>{triggerButton}</DialogTrigger>
        <DialogContent className="w-[90vw] p-4 max-h-[80vh]">
          <DialogHeader className="px-0">
            <DialogTitle className="text-lg font-bold">
              Filter Videos
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto px-1 pb-4">
            <FilterContent />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>{triggerButton}</PopoverTrigger>
      <PopoverContent
        className="w-[90vw] md:w-[min(500px,60vw)] p-4 bg-popover border-border max-h-[80vh] rounded-lg shadow-md"
        align="end"
        alignOffset={-5}
        sideOffset={5}
      >
        <h3 className="font-bold text-lg mb-3 text-left text-foreground">
          Filter Videos
        </h3>
        <FilterContent />
      </PopoverContent>
    </Popover>
  );
}