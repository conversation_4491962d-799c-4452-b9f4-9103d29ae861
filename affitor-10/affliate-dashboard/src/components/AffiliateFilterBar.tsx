import { useDispatch, useSelector } from "react-redux";
import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { Plus } from "lucide-react";
import {
  selectFilters,
  actions as filterActions,
} from "@/features/filter/filter.slice";
import { affiliateActions } from "@/features/rootActions";

import { StrapiClient } from "@/utils/request";

// RangeDropdown: summary view + input edit mode
function RangeDropdown({
  label,
  min,
  max,
  unit = "",
  onChange,
  placeholderMin = "Min",
  placeholderMax = "Max",
}: {
  label: string;
  min: string;
  max: string;
  unit?: string;
  onChange: (min: string, max: string) => void;
  placeholderMin?: string;
  placeholderMax?: string;
}) {
  const [open, setOpen] = useState(false);
  const [localMin, setLocalMin] = useState(min);
  const [localMax, setLocalMax] = useState(max);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setLocalMin(min);
    setLocalMax(max);
  }, [min, max]);

  function handleBlur(e: React.FocusEvent) {
    if (!containerRef.current?.contains(e.relatedTarget as Node)) {
      setOpen(false);
      onChange(localMin, localMax);
    }
  }

  function handleCollapse() {
    setOpen(false);
    onChange(localMin, localMax);
  }

  function summary() {
    if (localMin || localMax) {
      const minVal = localMin ? `${unit}${localMin}` : "Any";
      const maxVal = localMax ? `${unit}${localMax}${unit ? "+" : ""}` : (localMin ? (unit ? "+" : "Any") : "Any");
      return `${minVal} - ${maxVal}`;
    }
    return "Any";
  }

  return (
    <div className="flex flex-col min-w-0 flex-1 sm:min-w-[160px] lg:min-w-[180px]" ref={containerRef} tabIndex={-1} onBlur={handleBlur}>
      <label className="text-xs font-semibold mb-2 text-gray-700 dark:text-white">{label}</label>
      {open ? (
        <div className="flex gap-2">
          <input
            className="w-full border border-input dark:border-border bg-white dark:bg-input/30 rounded-md px-3 py-3 focus:ring-2 focus:ring-blue-200 focus:border-blue-500 transition-all duration-150 h-12 text-gray-900 dark:text-white outline-none text-[16px] md:text-sm placeholder:text-gray-400 dark:placeholder:text-muted-foreground"
            value={localMin}
            onChange={e => setLocalMin(e.target.value.replace(/[^0-9.]/g, ""))}
            placeholder={placeholderMin}
            autoFocus
            onKeyDown={e => e.key === "Enter" && handleCollapse()}
          />
          <input
            className="w-full border border-input dark:border-border bg-white dark:bg-input/30 rounded-md px-3 py-3 focus:ring-2 focus:ring-blue-200 focus:border-blue-500 transition-all duration-150 h-12 text-gray-900 dark:text-white outline-none text-[16px] md:text-sm placeholder:text-gray-400 dark:placeholder:text-muted-foreground"
            value={localMax}
            onChange={e => setLocalMax(e.target.value.replace(/[^0-9.]/g, ""))}
            placeholder={placeholderMax}
            onKeyDown={e => e.key === "Enter" && handleCollapse()}
          />
        </div>
      ) : (
        <button
          className="w-full border border-input dark:border-border bg-white dark:bg-input/30 rounded-md px-3 py-3 h-12 text-left text-[16px] md:text-sm flex items-center justify-between hover:bg-gray-50 dark:hover:bg-input/50 transition cursor-pointer text-gray-800 dark:text-white"
          onClick={() => setOpen(true)}
          type="button"
        >
          <span className="truncate text-gray-800 dark:text-white">{summary()}</span>
          <svg className="ml-2 w-4 h-4 text-gray-400 dark:text-muted-foreground" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
        </button>
      )}
    </div>
  );
}

// MultiSelectDropdown: summary button, expands to checkbox list, auto-saves on selection
function MultiSelectDropdown({ 
  label, 
  options, 
  selected, 
  onChange, 
  placeholder 
}: {
  label: string;
  options: string[];
  selected: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
}) {
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClick(e: MouseEvent) {
      if (ref.current && !ref.current.contains(e.target as Node)) setOpen(false);
    }
    if (open) document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [open]);

  function handleToggle(val: string) {
    const newSelection = selected.includes(val) 
      ? selected.filter(v => v !== val) 
      : [...selected, val];
    onChange(newSelection); // Auto-save immediately
  }

  function handleKeyDown(e: React.KeyboardEvent) {
    if (e.key === 'Enter' || e.key === 'Escape') setOpen(false);
  }

  const summary = useMemo(() => {
    if (!selected || selected.length === 0) return placeholder || 'Any';
    if (selected.length <= 2) return selected.join(', ');
    return `${selected.slice(0,2).join(', ')} +${selected.length-2}`;
  }, [selected, placeholder]);

  return (
    <div className={`relative min-w-0 flex-1 ${label === 'Country' ? 'sm:min-w-[240px] lg:min-w-[280px]' : 'sm:min-w-[160px] lg:min-w-[180px]'}`} ref={ref}>
      <label className="block text-xs font-medium mb-2 text-gray-700 dark:text-white">{label}</label>
      <button
        type="button"
        className="w-full px-3 py-3 border border-input dark:border-border rounded-md bg-white dark:bg-input/30 text-left hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-200 h-12 flex items-center justify-between text-gray-900 dark:text-white transition-all duration-150"
        onClick={() => setOpen(o => !o)}
      >
        <span className="truncate text-[16px] md:text-sm">{summary}</span>
        <svg className="ml-2 w-4 h-4 text-gray-400 dark:text-muted-foreground" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      {open && (
        <div
          className="absolute z-20 mt-1 w-full bg-white dark:bg-background border rounded shadow-lg max-h-56 overflow-auto p-2"
          tabIndex={0}
          onKeyDown={handleKeyDown}
        >
          {options.map(opt => (
            <label key={opt} className="flex items-center gap-2 py-2 px-2 hover:bg-gray-100 dark:hover:bg-muted rounded cursor-pointer">
              <input
                type="checkbox"
                checked={selected.includes(opt)}
                onChange={() => handleToggle(opt)}
                className="accent-blue-500"
              />
              <span className="text-sm text-gray-900 dark:text-foreground">{opt}</span>
            </label>
          ))}
        </div>
      )}
    </div>
  );
}

export default function AffiliateFilterBar() {
  const dispatch = useDispatch();
  const filters = useSelector(selectFilters);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [recurringDropdownOpen, setRecurringDropdownOpen] = useState(false);
  const recurringDropdownRef = useRef<HTMLDivElement>(null);

  // Close recurring dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (recurringDropdownRef.current && !recurringDropdownRef.current.contains(event.target as Node)) {
        setRecurringDropdownOpen(false);
      }
    }
    if (recurringDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [recurringDropdownOpen]);

  const defaultFilters = useMemo(
    () => ({
      pricing: { from: "", to: "" },
      commission: { from: "", to: "" },
      conversion: { from: "", to: "" },
      monthlyTraffic: { from: "", to: "" },
      cookiesDuration: { from: "", to: "" },
      category: "",
      paymentMethod: "",
      recurring: "",
      countries: [],
      launchYears: [],
    }),
    []
  );
  const [localFilters, setLocalFilters] = useState(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleRangeChange = useCallback((key: keyof typeof filters, min: string, max: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: { from: min, to: max },
    }));
  }, []);

  const handleRecurringSelect = useCallback((value: string) => {
    setLocalFilters((prev) => {
      if (prev.recurring === value) return prev;
      return {
        ...prev,
        recurring: value,
      };
    });
  }, []);

  const handleApplyFilter = useCallback(() => {
    dispatch(filterActions.setFilters(localFilters));
    dispatch(affiliateActions.setAffiliates(null));
  }, [dispatch, localFilters]);

  const handleClearAll = useCallback(() => {
    setLocalFilters(defaultFilters);
    dispatch(filterActions.resetFilters());
    dispatch(affiliateActions.setAffiliates(null));
  }, [dispatch, defaultFilters]);

  // Add state for country/launchYear options
  const [countryOptions, setCountryOptions] = useState<string[]>([]);
  const [launchYearOptions, setLaunchYearOptions] = useState<string[]>([]);
  useEffect(() => {
    console.log('Fetching countries and launch years...');
    StrapiClient.getCountries().then((countries: any) => {
      console.log('Countries fetched:', countries);
      setCountryOptions(countries as string[]);
    }).catch(err => console.error('Error fetching countries:', err));
    
    StrapiClient.getLaunchYears().then((years: any) => {
      console.log('Launch years fetched:', years);
      setLaunchYearOptions(years as string[]);
    }).catch(err => console.error('Error fetching launch years:', err));
  }, []);

  return (
    <div className="w-full bg-white dark:bg-background border border-gray-200 dark:border-border rounded-xl shadow-sm p-4 mb-6 flex flex-col gap-4">
      {/* Basic Filters Row: Commission, Recurring, Monthly Traffic, Country */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
        <RangeDropdown
          label="Commission (%)"
          min={localFilters.commission.from}
          max={localFilters.commission.to}
          unit="%"
          onChange={(min, max) => handleRangeChange("commission", min, max)}
        />
        <div className="flex flex-col min-w-0 flex-1 sm:min-w-[160px] lg:min-w-[180px]">
          <label className="text-xs font-semibold mb-2 text-gray-700 dark:text-white">Recurring</label>
          <div className="relative" ref={recurringDropdownRef}>
            <button
              type="button"
              className="w-full border border-input dark:border-border bg-white dark:bg-input/30 rounded-md px-3 py-3 h-12 text-left text-[16px] md:text-sm flex items-center justify-between hover:bg-gray-50 dark:hover:bg-input/50 transition-all duration-150 cursor-pointer text-gray-800 dark:text-white focus:ring-2 focus:ring-blue-200 focus:border-blue-500 focus:outline-none"
              onClick={() => setRecurringDropdownOpen(!recurringDropdownOpen)}
            >
              <span className="truncate">
                {localFilters.recurring === "one_time" ? "One time" :
                 localFilters.recurring === "life_time" ? "Life time" :
                 localFilters.recurring === "less_than_12" ? "Shorter than 12 months" :
                 localFilters.recurring === "more_than_or_equal_12" ? "12 months or longer" :
                 "Select recurring type..."}
              </span>
              <svg className="ml-2 w-4 h-4 text-gray-400 dark:text-muted-foreground" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {recurringDropdownOpen && (
              <div className="absolute z-20 mt-1 w-full bg-white dark:bg-background border border-gray-200 dark:border-border rounded-md shadow-lg max-h-56 overflow-auto">
                <div className="py-1">
                  <button
                    type="button"
                    className="w-full text-left px-3 py-2 text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-muted transition-colors"
                    onClick={() => {
                      handleRecurringSelect("one_time");
                      setRecurringDropdownOpen(false);
                    }}
                  >
                    One time
                  </button>
                  <button
                    type="button"
                    className="w-full text-left px-3 py-2 text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-muted transition-colors"
                    onClick={() => {
                      handleRecurringSelect("life_time");
                      setRecurringDropdownOpen(false);
                    }}
                  >
                    Life time
                  </button>
                  <button
                    type="button"
                    className="w-full text-left px-3 py-2 text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-muted transition-colors"
                    onClick={() => {
                      handleRecurringSelect("less_than_12");
                      setRecurringDropdownOpen(false);
                    }}
                  >
                    Shorter than 12 months
                  </button>
                  <button
                    type="button"
                    className="w-full text-left px-3 py-2 text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-muted transition-colors"
                    onClick={() => {
                      handleRecurringSelect("more_than_or_equal_12");
                      setRecurringDropdownOpen(false);
                    }}
                  >
                    12 months or longer
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
        <RangeDropdown
          label="Monthly Traffic"
          min={localFilters.monthlyTraffic.from}
          max={localFilters.monthlyTraffic.to}
          onChange={(min, max) => handleRangeChange("monthlyTraffic", min, max)}
        />
        <MultiSelectDropdown
          label="Country"
          options={countryOptions}
          selected={filters.countries || []}
          onChange={vals => dispatch(filterActions.updateFilter({ key: 'countries', value: vals }))}
          placeholder="Any"
        />
      </div>

      {/* Advanced Filters Row: EPU, Pricing, Cookies, Launch Year */}
      {showAdvanced && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
          <RangeDropdown
            label="EPU"
            min={localFilters.conversion.from}
            max={localFilters.conversion.to}
            unit="$"
            onChange={(min, max) => handleRangeChange("conversion", min, max)}
          />
          <RangeDropdown
            label="Pricing ($)"
            min={localFilters.pricing.from}
            max={localFilters.pricing.to}
            unit="$"
            onChange={(min, max) => handleRangeChange("pricing", min, max)}
          />
          <RangeDropdown
            label="Cookies (days)"
            min={localFilters.cookiesDuration.from}
            max={localFilters.cookiesDuration.to}
            unit="days"
            onChange={(min, max) => handleRangeChange("cookiesDuration", min, max)}
          />
          <MultiSelectDropdown
            label="Launch Year"
            options={launchYearOptions}
            selected={filters.launchYears || []}
            onChange={vals => dispatch(filterActions.updateFilter({ key: 'launchYears', value: vals }))}
            placeholder="Any"
          />
        </div>
      )}

      {/* Controls Row: Advanced Toggle (left), Buttons (right) */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between w-full gap-3 sm:gap-0 pt-2 border-t border-gray-100 dark:border-border/50">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 dark:text-white hover:text-gray-800 dark:hover:text-white transition-colors border border-gray-200 dark:border-border rounded-md bg-white dark:bg-input/30"
        >
          <Plus className="w-4 h-4" />
          <span>{showAdvanced ? 'Hide' : 'Show'} Advanced Filters</span>
        </button>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <button
            className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-200 text-sm w-full sm:w-auto"
            onClick={handleApplyFilter}
          >
            Apply Filters
          </button>
          <button
            className="bg-gray-100 dark:bg-input/30 text-gray-600 dark:text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-input/50 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-200 text-sm border border-gray-200 dark:border-border w-full sm:w-auto"
            onClick={handleClearAll}
          >
            Clear All
          </button>
        </div>
      </div>
    </div>
  );
} 