import React from 'react';
import { Button } from '@/components/ui/button';

interface LinkOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  url: string;
  onSelectOption: (option: 'url' | 'embed' | 'bookmark') => void;
  position?: { x: number; y: number };
}

const LinkOptionsModal: React.FC<LinkOptionsModalProps> = ({
  isOpen,
  onClose,
  url,
  onSelectOption,
  position = { x: 0, y: 0 }
}) => {
  if (!isOpen) return null;

  const handleOptionSelect = (option: 'url' | 'embed' | 'bookmark') => {
    onSelectOption(option);
    onClose();
  };

  // Get domain from URL for display
  const getDomain = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  // Check if URL is embeddable (YouTube, Vimeo, etc.)
  const isEmbeddable = (url: string) => {
    const embeddablePatterns = [
      /youtube\.com\/watch/,
      /youtu\.be\//,
      /vimeo\.com\//,
      /twitter\.com\//,
      /x\.com\//,
      /instagram\.com\//,
      /tiktok\.com\//,
      /codepen\.io\//,
      /codesandbox\.io\//,
      /figma\.com\//,
      /spotify\.com\//,
      /soundcloud\.com\//
    ];
    return embeddablePatterns.some(pattern => pattern.test(url));
  };

  const domain = getDomain(url);
  const canEmbed = isEmbeddable(url);

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-20 z-40"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div 
        className="fixed z-50 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-4 min-w-[280px] max-w-[320px]"
        style={{
          left: Math.min(position.x, window.innerWidth - 340),
          top: Math.min(position.y, window.innerHeight - 200),
        }}
      >
        <div className="space-y-3">
          <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            Create link to <span className="font-medium">{domain}</span>
          </div>
          
          {/* URL Option */}
          <Button
            variant="ghost"
            className="w-full justify-start h-auto p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700"
            onClick={() => handleOptionSelect('url')}
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <div>
                <div className="font-medium text-gray-900 dark:text-gray-100">Link</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Create a simple link</div>
              </div>
            </div>
          </Button>

          {/* Embed Option */}
          {canEmbed && (
            <Button
              variant="ghost"
              className="w-full justify-start h-auto p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700"
              onClick={() => handleOptionSelect('embed')}
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded flex items-center justify-center">
                  <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0V1" />
                  </svg>
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">Embed</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Embed the content directly</div>
                </div>
              </div>
            </Button>
          )}

          {/* Bookmark Option */}
          <Button
            variant="ghost"
            className="w-full justify-start h-auto p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700"
            onClick={() => handleOptionSelect('bookmark')}
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded flex items-center justify-center">
                <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                </svg>
              </div>
              <div>
                <div className="font-medium text-gray-900 dark:text-gray-100">Bookmark</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Create a rich preview card</div>
              </div>
            </div>
          </Button>

          {/* Dismiss Option */}
          <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
            <Button
              variant="ghost"
              className="w-full justify-center text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
              onClick={() => {
                // If user dismisses, just insert the URL as plain text
                onSelectOption('url');
              }}
            >
              Just paste as text
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default LinkOptionsModal;
