"use client";

import { useState, useEffect } from "react";

export default function ColumnsModal({ columns = [], handleToggleColumn }: {
  columns: { id: string; label: string; enabled: boolean }[];
  handleToggleColumn: (id: string) => void;
}) {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleToggleModal = () => setIsOpen((prev) => !prev);
    window.addEventListener("toggleColumnsModal", handleToggleModal);
    return () =>
      window.removeEventListener("toggleColumnsModal", handleToggleModal);
  }, []);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
      onClick={(e) => {
        if (e.target === e.currentTarget) setIsOpen(false);
      }}
    >
      <div className="bg-primary p-6 rounded-lg shadow-lg max-w-md w-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Customize Columns</h3>
          <button
            onClick={() => setIsOpen(false)}
            className="text-muted-foreground hover:text-foreground"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="space-y-2 mb-6">
          {columns.map((column) => (
            <div key={column.id} className="flex items-center">
              <input
                type="checkbox"
                id={column.id}
                checked={column.enabled}
                onChange={() => handleToggleColumn(column.id)}
                className="mr-2"
              />
              <label htmlFor={column.id}>{column.label}</label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
