"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectAiscriptOpen, selectPanelWidth, actions } from '@/features/aiscript/aiscript.slice';
import { useHeaderHeight } from '@/hooks/useHeaderHeight';
import AiScript from '../AiScript';

interface AiChatPortalProps {
  children?: React.ReactNode;
}

export default function AiChatPortal({ children }: AiChatPortalProps) {
  const [mounted, setMounted] = useState(false);
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isAnimating, setIsAnimating] = useState(true);
  const [shouldRender, setShouldRender] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const isAiOpen = useSelector(selectAiscriptOpen);
  const panelWidth = useSelector(selectPanelWidth);
  const dispatch = useDispatch();
  const { headerHeight } = useHeaderHeight();

  // Animation state management
  useEffect(() => {
    if (isAiOpen) {
      // Opening: render immediately, then animate in
      setShouldRender(true);

      // Trigger animation after DOM update
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 10);

      return () => clearTimeout(timer);
    } else if (shouldRender) {
      // Closing: start animation, then hide after completion
      setIsAnimating(true);

      const timer = setTimeout(() => {
        setShouldRender(false);
      }, 500); // Match animation duration

      return () => clearTimeout(timer);
    }
  }, [isAiOpen, shouldRender]);

  // Resize functionality (disabled on mobile)
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // Don't allow resizing on mobile
    if (isMobile) return;

    e.preventDefault();
    const startX = e.clientX;
    const startWidth = panelWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = startX - e.clientX;
      const viewportWidth = window.innerWidth;
      const maxWidth = Math.min(800, viewportWidth * 0.45);
      const minWidth = 320;
      const newWidth = Math.max(minWidth, Math.min(maxWidth, startWidth + deltaX));
      dispatch(actions.setPanelWidth(newWidth));
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    setIsDragging(true);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';
  }, [panelWidth, dispatch, isMobile]);

  // Check for mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    // Create or get the portal container
    let container = document.getElementById('ai-chat-portal');
    if (!container) {
      container = document.createElement('div');
      container.id = 'ai-chat-portal';
      container.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 9999;
      `;
      document.body.appendChild(container);
    }

    setPortalContainer(container);
    setMounted(true);

    // Apply body margin when chat is open to push content left (only on desktop)
    const applyBodyMargin = () => {
      if (isAiOpen && !isAnimating && !isMobile) {
        // Opening: smooth slide (only on desktop)
        document.body.style.marginRight = `${panelWidth}px`;
        document.body.style.transition = 'margin-right 500ms cubic-bezier(0.16, 1, 0.3, 1)';
      } else if (!isAiOpen || isMobile) {
        // Closing: smooth slide back or mobile (no margin)
        document.body.style.marginRight = '0';
        document.body.style.transition = 'margin-right 500ms cubic-bezier(0.7, 0, 0.84, 0)';
      }
    };

    applyBodyMargin();

    return () => {
      // Clean up portal container and body margin when component unmounts
      document.body.style.marginRight = '0';
      document.body.style.transition = '';

      const existingContainer = document.getElementById('ai-chat-portal');
      if (existingContainer && existingContainer.children.length === 0) {
        document.body.removeChild(existingContainer);
      }
    };
  }, [isAiOpen, panelWidth, isAnimating, isMobile]);

  if (!mounted || !portalContainer) {
    return null;
  }

  return createPortal(
    <div 
      className="fixed inset-0 pointer-events-none"
      style={{ zIndex: 9999 }}
    >
      {/* Floating Button Container */}
      {!isAiOpen && (
        <div 
          className="fixed bottom-4 right-4 pointer-events-auto"
          style={{ zIndex: 10000 }}
        >
          <AiScript />
        </div>
      )}
      
      {/* Chat Panel Container */}
      {shouldRender && (
        <div
          className="fixed pointer-events-auto flex"
          style={{
            top: `${headerHeight}px`,
            height: `calc(100vh - ${headerHeight}px)`,
            width: isMobile ? '100vw' : `${panelWidth}px`,
            right: '0',
            left: isMobile ? '0' : 'auto',
            zIndex: 10000,
            transform: (isAiOpen && !isAnimating) ? 'translateX(0)' : 'translateX(100%)',
            transition: 'transform 500ms',
            transitionTimingFunction: isAiOpen
              ? 'cubic-bezier(0.16, 1, 0.3, 1)'
              : 'cubic-bezier(0.7, 0, 0.84, 0)'
          }}
        >
          {/* Resize Handle - Hidden on mobile */}
          {!isMobile && (
            <div
              className="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-blue-500 dark:hover:bg-blue-400 cursor-ew-resize transition-colors duration-200 flex-shrink-0"
              onMouseDown={handleMouseDown}
              style={{ cursor: isDragging ? 'ew-resize' : 'ew-resize' }}
            />
          )}

          {/* Chat Panel Content */}
          <div className={`flex-1 h-full bg-white dark:bg-gray-900 shadow-xl ${
            isMobile ? '' : 'border-l border-gray-200 dark:border-gray-700'
          }`}>
            <AiScript />
          </div>
        </div>
      )}
    </div>,
    portalContainer
  );
}
