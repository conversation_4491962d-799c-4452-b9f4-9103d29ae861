export interface AffiliateProgram {
  id: number;
  name: string;
  logo: string;
  description: string;
  established: number;
  commission: string;
  payoutTime: string;
  monthlyTraffic: string;
  category: string;
  url: string;
  affiliateUrl: string;
  trafficData: any[];
  paymentMethods: PaymentMethod[];
  minimumPayout: number;
  launchYear: number;
  cookieDuration: string;
  contactInfo?: ContactInformation;
  trustScore: number;
  trafficRank: number;
  tagLine: string;
  companyName: string;
  
}

export interface PaymentMethod {
  name: string;
  icon: string;
  class: string;
}

export interface ContactInformation {
  email: string;
  phone: string;
}