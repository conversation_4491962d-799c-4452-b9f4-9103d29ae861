import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/store';

export interface QuickReply {
  label: string;
  content: string;
  promptId?: number | undefined; // Optional promptId for quick replies
}

export interface Message {
  type: 'user' | 'ai';
  content: string;
  quickReplies?: QuickReply[];
  copyable?: boolean;
}

export interface Prompt {
  id: number;
  documentId: string;
  title: string;
  content: string;
  description: string | null;
}

export interface UserPrompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  tags: string[];
  isFavorite: boolean;
  usageCount: number;
  lastUsedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PromptManagerState {
  isOpen: boolean;
  searchQuery: string;
  selectedTags: string[];
  view: 'all' | 'favorites' | 'recent' | 'templates';
  isLoading: boolean;
  error: string | null;
}

export interface SavePromptModalState {
  isOpen: boolean;
  sourceMessage?: string;
  isLoading: boolean;
}

export interface UserPromptAction {
  displayText: string;
  content: string;
  promptId?: number; // Add promptId as an optional field
}



interface AiscriptState {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  isOpen: boolean;
  quickReplies: QuickReply[];
  prompts: Prompt[];
  promptsLoading: boolean;
  sessionId: string | null;

  // User prompt management
  userPrompts: UserPrompt[];
  userPromptsLoading: boolean;
  userPromptsError: string | null;
  availableTags: string[];
  recentPrompts: UserPrompt[];

  // UI state
  panelWidth: number;
  isMaximized: boolean;
  promptManager: PromptManagerState;
  savePromptModal: SavePromptModalState;
}

// Helper function to get panel width from localStorage
const getInitialPanelWidth = (): number => {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('affitor_ai_panel_width');
    if (saved) {
      const width = parseInt(saved, 10);
      // Ensure width is within constraints
      const maxWidth = Math.min(800, window.innerWidth * 0.45);
      return Math.max(320, Math.min(width, maxWidth));
    }
  }
  return 380; // Default width
};

const initialState: AiscriptState = {
  messages: [],
  isLoading: false,
  error: null,
  isOpen: false,
  quickReplies: [],
  prompts: [],
  promptsLoading: false,
  sessionId: null,

  // User prompt management
  userPrompts: [],
  userPromptsLoading: false,
  userPromptsError: null,
  availableTags: [],
  recentPrompts: [],

  // UI state
  panelWidth: getInitialPanelWidth(),
  isMaximized: false,
  promptManager: {
    isOpen: false,
    searchQuery: '',
    selectedTags: [],
    view: 'all',
    isLoading: false,
    error: null,
  },
  savePromptModal: {
    isOpen: false,
    sourceMessage: undefined,
    isLoading: false,
  },
};

const aiscriptSlice = createSlice({
  name: 'aiscript',
  initialState,
  reducers: {
    sendMessage: (state, action: PayloadAction<string>) => {
      // Add user message to messages
      state.messages.push({ type: 'user', content: action.payload });
      state.error = null;
    },
    setMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
    },
    setMessageWithQuickReplies: (state, action: PayloadAction<{content: string, quickReplies: QuickReply[]}>) => {
      state.messages.push({
        type: 'ai',
        content: action.payload.content,
        quickReplies: action.payload.quickReplies
      });
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      
      if (action.payload) {
        state.messages.push({
          type: 'ai',
          content: "I'm having trouble connecting. Please try again later."
        });
      }
    },
    clearMessages: (state) => {
      state.messages = [];
    },
    openAIScript: (state) => {
      state.isOpen = true;
      // Ensure panel opens at default width of 380px
      state.panelWidth = 380;

      // Persist to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('affitor_ai_panel_width', '380');
      }
    },
    closeAIScript: (state) => {
      state.isOpen = false;
    },
    toggleScript: (state) => {
      state.isOpen = !state.isOpen;
    },
    setQuickReplies: (state, action: PayloadAction<QuickReply[]>) => {
      state.quickReplies = action.payload;
    },
    fetchPrompts: (state) => {
      state.promptsLoading = true;
    },
    setPrompts: (state, action: PayloadAction<Prompt[]>) => {
      state.prompts = action.payload;
      state.promptsLoading = false;
      
      // Convert prompts to quick replies format
      const quickReplies: QuickReply[] = action.payload.map(prompt => ({
        label: prompt.title,
        content: prompt.content
      }));
      
      state.quickReplies = quickReplies;
    },
    setPromptsError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.promptsLoading = false;
    },
    endSession: (state) => {
      // This will be handled by saga
      state.isOpen = false;
      state.messages = [];
      state.quickReplies = [];
    },
    sendUserPrompt: (state, action: PayloadAction<UserPromptAction>) => {
      console.log(action.payload);
      // Add user message with only the display text
      console.log('LOG-state.messages', state.messages);
      state.messages.push({ 
        type: 'user', 
        content: action.payload.displayText || action.payload.content,
      }); 

      console.log('LOG-state.messages', state.messages);
      state.error = null;
    },
    setSessionId: (state, action: PayloadAction<string | null>) => {
      state.sessionId = action.payload;
    },

    // User prompt management actions
    fetchUserPrompts: (state) => {
      state.userPromptsLoading = true;
      state.userPromptsError = null;
    },
    setUserPrompts: (state, action: PayloadAction<UserPrompt[]>) => {
      state.userPrompts = action.payload;
      state.userPromptsLoading = false;
      state.userPromptsError = null;
    },
    setUserPromptsError: (state, action: PayloadAction<string>) => {
      state.userPromptsError = action.payload;
      state.userPromptsLoading = false;
    },
    setUserPromptsLoading: (state, action: PayloadAction<boolean>) => {
      state.userPromptsLoading = action.payload;
    },
    addUserPrompt: (state, action: PayloadAction<UserPrompt>) => {
      state.userPrompts.unshift(action.payload);
    },
    updateUserPrompt: (state, action: PayloadAction<UserPrompt>) => {
      const index = state.userPrompts.findIndex(p => p.id === action.payload.id);
      if (index !== -1) {
        state.userPrompts[index] = action.payload;
      }
    },
    updateUserPromptRequest: (state, action: PayloadAction<{id: string, data: any}>) => {
      // This will be handled by saga to call API
      state.userPromptsLoading = true;
      state.userPromptsError = null;
    },
    deleteUserPromptRequest: (state, action: PayloadAction<string>) => {
      // This will be handled by saga to call API
      state.userPromptsLoading = true;
      state.userPromptsError = null;
    },
    removeUserPrompt: (state, action: PayloadAction<string>) => {
      state.userPrompts = state.userPrompts.filter(p => p.id !== action.payload);
    },
    deleteUserPrompt: (state, action: PayloadAction<string>) => {
      state.userPrompts = state.userPrompts.filter(p => p.id !== action.payload);
    },
    createUserPrompt: (state, action: PayloadAction<Omit<UserPrompt, 'id' | 'usageCount' | 'createdAt' | 'updatedAt' | 'lastUsedAt'>>) => {
      // This will be handled by saga to call API
      state.userPromptsLoading = true;
      state.userPromptsError = null;
    },
    toggleUserPromptFavorite: (state, action: PayloadAction<string>) => {
      const prompt = state.userPrompts.find(p => p.id === action.payload);
      if (prompt) {
        prompt.isFavorite = !prompt.isFavorite;
      }
    },
    incrementPromptUsage: (state, action: PayloadAction<string>) => {
      const prompt = state.userPrompts.find(p => p.id === action.payload);
      if (prompt) {
        prompt.usageCount += 1;
        prompt.lastUsedAt = new Date().toISOString();
      }
    },
    setAvailableTags: (state, action: PayloadAction<string[]>) => {
      state.availableTags = action.payload;
    },
    setRecentPrompts: (state, action: PayloadAction<UserPrompt[]>) => {
      state.recentPrompts = action.payload;
    },

    // UI state actions
    setPanelWidth: (state, action: PayloadAction<number>) => {
      const width = action.payload;
      // Ensure width is within constraints
      const maxWidth = typeof window !== 'undefined' ? Math.min(800, window.innerWidth * 0.45) : 800;
      state.panelWidth = Math.max(320, Math.min(width, maxWidth));

      // Persist to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('affitor_ai_panel_width', state.panelWidth.toString());
      }
    },
    setMaximized: (state, action: PayloadAction<boolean>) => {
      state.isMaximized = action.payload;
    },

    // Prompt manager actions
    openPromptManager: (state) => {
      state.promptManager.isOpen = true;
      // Only set loading if we don't have prompts yet
      if (state.userPrompts.length === 0) {
        state.userPromptsLoading = true;
        state.userPromptsError = null;
      }
    },
    closePromptManager: (state) => {
      state.promptManager.isOpen = false;
      state.promptManager.searchQuery = '';
      state.promptManager.selectedTags = [];
      state.promptManager.error = null;
    },
    setPromptManagerSearch: (state, action: PayloadAction<string>) => {
      state.promptManager.searchQuery = action.payload;
    },
    setPromptManagerTags: (state, action: PayloadAction<string[]>) => {
      state.promptManager.selectedTags = action.payload;
    },
    setPromptManagerView: (state, action: PayloadAction<'all' | 'favorites' | 'recent' | 'templates'>) => {
      state.promptManager.view = action.payload;
    },
    setPromptManagerLoading: (state, action: PayloadAction<boolean>) => {
      state.promptManager.isLoading = action.payload;
    },
    setPromptManagerError: (state, action: PayloadAction<string | null>) => {
      state.promptManager.error = action.payload;
    },

    // Save prompt modal actions
    openSavePromptModal: (state, action: PayloadAction<string>) => {
      state.savePromptModal.isOpen = true;
      state.savePromptModal.sourceMessage = action.payload;
    },
    closeSavePromptModal: (state) => {
      state.savePromptModal.isOpen = false;
      state.savePromptModal.sourceMessage = undefined;
      state.savePromptModal.isLoading = false;
    },
    setSavePromptModalLoading: (state, action: PayloadAction<boolean>) => {
      state.savePromptModal.isLoading = action.payload;
    },
  },
});

export const { actions } = aiscriptSlice; 

// Define base selector
const selectAiscriptState = (state: RootState) => state.aiscript;

// Create and export selectors
export const selectAiscriptMessages = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.messages
);

export const selectAiscriptLoading = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.isLoading
);

export const selectAiscriptError = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.error
);

export const selectAiscriptOpen = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.isOpen
);

export const selectAiscriptQuickReplies = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.quickReplies
);

export const selectAiscriptPrompts = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.prompts
);

export const selectPromptsLoading = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.promptsLoading
);

export const selectSessionId = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.sessionId
);

// User prompt selectors
export const selectUserPrompts = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.userPrompts
);

export const selectUserPromptsLoading = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.userPromptsLoading
);

export const selectUserPromptsError = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.userPromptsError
);

export const selectAvailableTags = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.availableTags
);

export const selectRecentPrompts = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.recentPrompts
);

// UI state selectors
export const selectPanelWidth = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.panelWidth
);

export const selectIsMaximized = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.isMaximized
);

export const selectPromptManager = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.promptManager
);

export const selectSavePromptModal = createSelector(
  [selectAiscriptState],
  (aiscriptState) => aiscriptState.savePromptModal
);

// Export the reducer
export const aiscriptReducer = aiscriptSlice.reducer;
export default aiscriptReducer;
