import { IAffiliate, IPagination, ISort } from "@/interfaces";
import { createSelector } from "reselect";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface AffiliateState {
  list: IAffiliate[] | null;
  loading: boolean;
  currentAffiliate: IAffiliate | null;
  error: string | null;
  pagination?: IPagination;
  currentUrl: string | null;
  loadingUrl: boolean;
  loadingUrlId: string | null;
  // Store URLs per affiliate ID for anchor tags
  affiliateUrls: Record<string, string>;
  // New summary-related state
  currentSummary: string | null;
  loadingSummary: boolean;
  loadingSummaryId: string | null;
}

const initialState: AffiliateState = {
  list: null,
  error: null,
  loading: false,
  currentAffiliate: null,
  currentUrl: null,
  loadingUrl: false,
  loadingUrlId: null,
  // Initialize affiliate URLs cache
  affiliateUrls: {},
  // Initialize new summary-related state
  currentSummary: null,
  loadingSummary: false,
  loadingSummaryId: null,
};

const affiliateSlice = createSlice({
  name: "affiliate",
  initialState,
  reducers: {
    // Sync actions
    setAffiliates: (state, action: PayloadAction<IAffiliate[] | null>) => {
      if (state.list && state.list.length && action.payload) {
        const ids = new Set(state.list.map((item) => item.documentId));
        state.list = [
          ...state.list,
          ...action.payload.filter((item) => !ids.has(item.documentId)),
        ];
      } else {
        state.list = action.payload;
      }
    },

    setCurrentAffiliate: (state, action: PayloadAction<IAffiliate | null>) => {
      console.log("setCurrentAffiliate", action.payload);
      state.currentAffiliate = action.payload;
    },
    // Trigger actions for Saga
    fetch: (
      state,
      action: PayloadAction<{
        pagination?: IPagination;
        sort?: ISort[];
        filters?: Record<string, any>;
      }>
    ) => {},

    fetchDetail: (state, action: PayloadAction<{ id: string }>) => {},
    setAffiliatePagination: (state, action: PayloadAction<IPagination>) => {
      state.pagination = action.payload;
    },
    setLoadingAffiliate: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // Updated URL fetching actions
    fetchAffiliateUrl: (state, action: PayloadAction<{ id: string; shouldOpen?: boolean }>) => {
      state.loadingUrl = true;
      state.loadingUrlId = action.payload.id; // Store the specific ID being loaded
      console.log("Setting loadingUrlId to:", action.payload.id); // Debug log
    },
    fetchAffiliateUrlFulfilled: (
      state,
      action: PayloadAction<{ url: string; id: string }>
    ) => {
      // Only store URL per affiliate ID for caching, don't set currentUrl
      // currentUrl should only be set via setAffiliateUrl when shouldOpen is true
      state.affiliateUrls[action.payload.id] = action.payload.url;
      state.loadingUrl = false;
      state.loadingUrlId = null; // Clear ID when done
    },
    setAffiliateUrl: (state, action: PayloadAction<string | null>) => {
      state.currentUrl = action.payload;
      state.loadingUrl = false;
      state.loadingUrlId = null; // Clear ID when done
    },
    setLoadingAffiliateUrl: (state, action: PayloadAction<boolean>) => {
      state.loadingUrl = action.payload;
      if (!action.payload) {
        state.loadingUrlId = null; // Clear ID when turning off loading
        console.log("Redux: Clearing loadingUrlId"); // Debug log
      }
    },

    // New summary fetching actions
    fetchAffiliateSummary: (state, action: PayloadAction<{ id: string }>) => {
      state.loadingSummary = true;
      state.loadingSummaryId = action.payload.id;
    },
    setAffiliateSummary: (state, action: PayloadAction<string | null>) => {
      state.currentSummary = action.payload;
      state.loadingSummary = false;
      state.loadingSummaryId = null;
    },
    setLoadingAffiliateSummary: (state, action: PayloadAction<boolean>) => {
      state.loadingSummary = action.payload;
      if (!action.payload) {
        state.loadingSummaryId = null;
      }
    },
  },
});

export const { actions, reducer } = affiliateSlice;
const selectAffiliateState = (state: RootState) => state.affiliate;

// affiliate
export const selectAffiliatePrograms = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.list
);

export const selectLoadingAffiliatePrograms = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.loading
);

export const selectCurrentAffiliate = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.currentAffiliate
);

export const selectAffiliatePagination = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.pagination
);

export const selectErrorAffiliate = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.error
);

// New selectors
export const selectAffiliateUrl = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.currentUrl
);

export const selectLoadingAffiliateUrl = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.loadingUrl
);

// New selector for the loading URL ID
export const selectLoadingAffiliateUrlId = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.loadingUrlId
);

// Selector to get URL for a specific affiliate ID
export const selectAffiliateUrlById = createSelector(
  [selectAffiliateState, (state, affiliateId: string) => affiliateId],
  (affiliateState, affiliateId) => affiliateState.affiliateUrls[affiliateId] || null
);

// New selectors for summary
export const selectAffiliateSummary = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.currentSummary
);

export const selectLoadingAffiliateSummary = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.loadingSummary
);

export const selectLoadingAffiliateSummaryId = createSelector(
  [selectAffiliateState],
  (affiliateState) => affiliateState.loadingSummaryId
);
