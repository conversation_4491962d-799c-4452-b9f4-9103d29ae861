import { call, put, takeLatest } from "redux-saga/effects";
import axios from "axios";
import { actions as referrerActions } from "./referrer.slice";

function* registerReferrerSaga() {
  try {
    // Get token from localStorage
    const token = typeof window !== "undefined" 
      ? localStorage.getItem("auth_token") 
      : null;
    
    if (!token) {
      throw new Error("Authentication required");
    }

    // Call the API endpoint to register as referrer
    yield call(axios.post, "/api/referrers", {}, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    // Dispatch success action
    yield put(referrerActions.registerReferrerSuccess());
  } catch (error: any) {
    // Extract error message
    const errorMessage = error.response?.data?.error || 
                         error.message || 
                         "Failed to register as referrer";
    
    // Dispatch failure action with error message
    yield put(referrerActions.registerReferrerFailure(errorMessage));
  }
}

export default function* referrerSaga() {
  yield takeLatest(referrerActions.registerReferrer.type, registerReferrerSaga);
}
