import { RootState } from "@/store";
import { createSelector, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ISubscriptionTier, IMeta } from "@/interfaces";

// Define current subscription interface with additional properties
export interface ICurrentSubscription extends ISubscriptionTier {
  renewal_date?: string;
  request_count?: number;
}

// Add this interface for comparison plans
export interface IComparisonPlan {
  id: number;
  documentId: string;
  name: string;
  traffic_share_rate: number | null;
  stripe_recurring_interval: string | null;
}

interface SubscriptionTierState {
  list: ISubscriptionTier[];
  loading: boolean;
  error: string | null;
  meta: IMeta | null;
  currentSubscription: ICurrentSubscription | null;
  comparisonPlans: IComparisonPlan[];
  comparisonPlansLoading: boolean;
  comparisonPlansError: string | null;
}

const initialState: SubscriptionTierState = {
  list: [],
  loading: false,
  error: null,
  meta: null,
  currentSubscription: null,
  comparisonPlans: [],
  comparisonPlansLoading: false,
  comparisonPlansError: null,
};

const subscriptionTierSlice = createSlice({
  name: "subscriptionTier",
  initialState,
  reducers: {
    // Fetch subscription tiers
    fetchSubscriptionTiers: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchSubscriptionTiersSuccess: (
      state,
      action: PayloadAction<{ data: ISubscriptionTier[]; meta: IMeta }>
    ) => {
      state.list = action.payload.data;
      state.meta = action.payload.meta;
      state.loading = false;
      state.error = null;
    },
    fetchSubscriptionTiersFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Set current subscription from user data
    setCurrentSubscription: (
      state,
      action: PayloadAction<ICurrentSubscription | null>
    ) => {
      state.currentSubscription = action.payload;
    },

    // Subscribe to a tier
    // Ensure tierId is always a string
    subscribeTier: (
      state,
      action: PayloadAction<{ tierId: number; paymentMethod: string }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    subscribeTierSuccess: (
      state,
      action: PayloadAction<ICurrentSubscription>
    ) => {
      state.loading = false;
      state.currentSubscription = action.payload;
    },
    subscribeTierFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Cancel subscription
    cancelSubscription: (state) => {
      state.loading = true;
      state.error = null;
    },
    cancelSubscriptionSuccess: (state) => {
      state.loading = false;
      state.currentSubscription = null;
    },
    cancelSubscriptionFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Add new reducers for comparison plans
    fetchComparisonPlans: (state) => {
      state.comparisonPlansLoading = true;
      state.comparisonPlansError = null;
    },

    setComparisonPlans: (state, action: PayloadAction<IComparisonPlan[]>) => {
      state.comparisonPlans = action.payload;
      state.comparisonPlansLoading = false;
      state.comparisonPlansError = null;
    },

    setComparisonPlansError: (state, action: PayloadAction<string>) => {
      state.comparisonPlansError = action.payload;
      state.comparisonPlansLoading = false;
    },
  },
});

export const { actions, reducer } = subscriptionTierSlice;

// Selectors
const selectSubscriptionTierState = (state: RootState) =>
  state.subscriptionTier;

export const selectSubscriptionTiers = createSelector(
  [selectSubscriptionTierState],
  (state) => state.list
);

export const selectSubscriptionTiersLoading = createSelector(
  [selectSubscriptionTierState],
  (state) => state.loading
);

export const selectSubscriptionTiersError = createSelector(
  [selectSubscriptionTierState],
  (state) => state.error
);

export const selectSubscriptionTiersMeta = createSelector(
  [selectSubscriptionTierState],
  (state) => state.meta
);

// Current subscription selector
export const selectCurrentSubscription = createSelector(
  [selectSubscriptionTierState],
  (state) => state.currentSubscription
);

// Add new selectors for comparison plans
export const selectComparisonPlans = createSelector(
  [(state: RootState) => state.subscriptionTier],
  (subscriptionTier) => subscriptionTier.comparisonPlans
);

export const selectComparisonPlansLoading = createSelector(
  [(state: RootState) => state.subscriptionTier],
  (subscriptionTier) => subscriptionTier.comparisonPlansLoading
);

export const selectComparisonPlansError = createSelector(
  [(state: RootState) => state.subscriptionTier],
  (subscriptionTier) => subscriptionTier.comparisonPlansError
);
