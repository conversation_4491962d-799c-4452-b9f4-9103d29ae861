@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth scrolling for horizontal tabs */
.overflow-x-auto {
  scroll-behavior: smooth;
}

/* Enhanced mobile tab scrolling */
@media (max-width: 768px) {
  .mobile-tabs-container {
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
  }

  .mobile-tab-item {
    scroll-snap-align: start;
  }
}

/* Modal scrollbar styling */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode scrollbar */
.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

/* Modal content smooth scrolling */
.modal-content {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Dark theme colors */
:root {
    --dark-bg: #0c0e14;
    --darker-bg: #070a12;
    --light-text: #f8f9fa;
    --secondary-text: #a1a7bb;
    --border-color: #1e2235;
    --primary-color: #3861fb;
    --tw-shadow-color: #0000001a;
    --hover-bg: #1a1f30;
    --negative-color: #ea3943;
    --positive-color: #16c784;
    --card-bg: #131722;
    --overlay-bg: rgba(19, 23, 34, 0.8);
    --box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    --highlight-border: 1px solid rgba(56, 97, 251, 0.1);
    --background: #ffffff;
    --foreground: #252525;
    --card: #ffffff;
    --card-foreground: #252525;
    --popover: #ffffff;
    --popover-foreground: #252525;
    --primary: #ffffff;
    --primary-foreground: #000000;
    --secondary: #f7f7f7;
    --secondary-foreground: #353535;
    --muted: #f7f7f7;
    --muted-foreground: #8e8e8e;
    --accent: #f7f7f7;
    --accent-foreground: #353535;
    --destructive: #ff0000;
    --destructive-foreground: #ff0000;
    --border: #ebebeb;
    --input: #ebebeb;
    --ring: #b3b3b3;
    --chart-1: #ff0000;
    --chart-2: #0000ff;
    --chart-3: #0000ff;
    --chart-4: #ff0000;
    --chart-5: #ff0000;
    --radius: 0.625rem;
    --sidebar: #f7f7f7;
    --sidebar-foreground: #252525;
    --sidebar-primary: #353535;
    --sidebar-primary-foreground: #f7f7f7;
    --sidebar-accent: #f7f7f7;
    --sidebar-accent-foreground: #353535;
    --sidebar-border: #ebebeb;
    --sidebar-ring: #b3b3b3;
}

/* Light theme colors */
.light {
    --dark-bg: #ffffff;
    --darker-bg: #ffffff;
    --light-text: #1a1f30;
    --secondary-text: #5c6278;
    --border-color: #e4e9f0;
    --primary-color: #3861fb;
    --hover-bg: #f5f7fa;
    --negative-color: #ea3943;
    --positive-color: #16c784;
    --card-bg: #ffffff;
    --overlay-bg: rgba(255, 255, 255, 0.8);
    --box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
    --highlight-border: 1px solid rgba(56, 97, 251, 0.05);
}

/* Base body styles */
body {
    background-color: var(--dark-bg);
    color: var(--light-text);
    line-height: 1.6;
    transition: all 0.3s ease;
    letter-spacing: 0.01em;
}

.dark {
    background-color: var(--dark-bg);
    --background: #252525;
    --foreground: #f7f7f7;
    --card: #252525;
    --card-foreground: #f7f7f7;
    --tw-shadow-color: #f7f7f7;
    --popover: #252525;
    --popover-foreground: #f7f7f7;
    --primary: #252525;
    --primary-foreground: #ffffff;
    --secondary: #434343;
    --secondary-foreground: #f7f7f7;
    --muted: #434343;
    --muted-foreground: #b3b3b3;
    --accent: #434343;
    --accent-foreground: #f7f7f7;
    --destructive: #ff0000;
    --destructive-foreground: #ff0000;
    --border: #434343;
    --input: #434343;
    --ring: #707070;
    --chart-1: #0000ff;
    --chart-2: #00ff00;
    --chart-3: #ff0000;
    --chart-4: #0000ff;
    --chart-5: #ff0000;
    --sidebar: #353535;
    --sidebar-foreground: #f7f7f7;
    --sidebar-primary: #0000ff;
    --sidebar-primary-foreground: #f7f7f7;
    --sidebar-accent: #434343;
    --sidebar-accent-foreground: #f7f7f7;
    --sidebar-border: #434343;
    --sidebar-ring: #707070;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

