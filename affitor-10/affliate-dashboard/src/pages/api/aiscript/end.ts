import type { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST method
  if (req.method !== "POST") {
    return res
      .status(405)
      .json({ statusCode: 405, message: "Method not allowed" });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    console.log("Ending AIScript session with sessionId:", req.body.sessionId);

    try {
      // Use StrapiClient to end the session
      const response = await StrapiClient.endScriptSession(
        token!,
        req.body.sessionId
      );
      console.log(response);

      // Return success
      return res.status(200).json({ success: true });
    } catch (apiError: any) {
      console.error("API Client error:", apiError);
      return sendApiError(res, apiError, "Failed to end script session");
    }
  } catch (error: any) {
    console.error("AIScript end session API error:", error);
    return sendApiError(
      res,
      error,
      "An error occurred while ending the session"
    );
  }
}
