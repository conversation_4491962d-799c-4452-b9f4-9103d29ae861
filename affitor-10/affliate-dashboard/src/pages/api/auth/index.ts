import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== "POST") {
    return res
      .status(405)
      .json({ statusCode: 405, message: "Method not allowed" });
  }

  try {
    const { action, ...data } = req.body;

    console.log("Received action:", action);
    console.log("Request data:", {
      ...data,
      password: data.password ? "[HIDDEN]" : undefined,
    });

    switch (action) {
      case "signin": {
        const { email, password } = data;
        if (!email || !password) {
          return res.status(400).json({
            statusCode: 400,
            message: "Email and password are required",
          });
        }

        // Use StrapiClient instead of axios
        const response = await StrapiClient.signin(email, password);
        return res.status(200).json(response);
      }

      case "signup": {
        const { fullName, email, password } = data;
        if (!fullName || !email || !password) {
          return res.status(400).json({
            statusCode: 400,
            message: "Full name, email and password are required",
          });
        }

        // Use StrapiClient instead of axios
        const response = await StrapiClient.signup({
          fullName,
          email,
          password,
        });
        return res.status(200).json(response);
      }

      case "firebase-auth": {
        const { idToken } = data;
        if (!idToken) {
          return res.status(400).json({
            statusCode: 400,
            message: "Firebase ID token is required",
          });
        }

        console.log(
          "Processing Firebase auth with token length:",
          idToken.length
        );

        // Get cookie string from request to forward to Strapi
        const cookies = req.headers.cookie || "";

        // Use StrapiClient with cookies
        const response = await StrapiClient.authenticateWithFirebaseToken(
          idToken,
          cookies
        );

        if (response.jwt && response.user) {
          return res.status(200).json({
            jwt: response.jwt,
            user: response.user,
            message: "Authentication successful",
          });
        } else {
          console.error("Invalid response from Strapi:", response);
          return res.status(401).json({
            statusCode: 401,
            message:
              "Authentication failed - invalid response from authentication service",
          });
        }
      }

      case "google-auth-url": {
        // Use StrapiClient instead of axios
        const response = await StrapiClient.getGoogleAuthUrl();
        return res.status(200).json(response);
      }

      default:
        return res
          .status(400)
          .json({ statusCode: 400, message: "Invalid action" });
    }
  } catch (error: any) {
    console.error("API route error:", error);
    sendApiError(res, error, "Authentication service error");
  }
}
