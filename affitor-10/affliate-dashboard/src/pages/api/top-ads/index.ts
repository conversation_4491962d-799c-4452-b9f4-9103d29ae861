import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { createApiContext } from "@/utils/api-middleware";
import qs from "qs";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Use centralized context creation
    const { token, cookies } = createApiContext(req, { forwardCookies: true });

    // Serialize the query object into a query string
    const queryString = qs.stringify(req.query, { encodeValuesOnly: true });

    // Pass token and cookies to StrapiClient
    const response = await StrapiClient.getTopAds(
      queryString,
      token || undefined,
      cookies
    );

    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching top ads data:", error);
    sendApiError(res, error, "Error fetching top ads data");
  }
}
