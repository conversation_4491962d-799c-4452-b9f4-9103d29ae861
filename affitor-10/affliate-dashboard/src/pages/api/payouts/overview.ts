import { NextApiRequest, NextApiResponse } from "next";

const STRAPI_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === "GET") {
    try {
      const { authorization } = req.headers;

      if (!authorization) {
        return res.status(401).json({ error: "Authorization token required" });
      }

      // Forward the request to Strapi
      const response = await fetch(`${STRAPI_URL}/api/payouts/overview`, {
        method: "GET",
        headers: {
          Authorization: authorization,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return res.status(response.status).json(errorData);
      }

      const data = await response.json();
      return res.status(200).json(data);
    } catch (error) {
      console.error("Payout overview API error:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
  } else {
    res.setHeader("Allow", ["GET"]);
    return res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}
