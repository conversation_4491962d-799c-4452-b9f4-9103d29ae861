import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import SignInContainer from "@/containers/Authentication/SignIn";
import { useDispatch, useSelector } from "react-redux";
import { actions as discourseActions } from "@/features/discourse/discourse.slice";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";
import { getRedirectUrlAfterAuth } from "@/utils/authRedirect";

const AuthenticationPage: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const [discourseParams, setDiscourseParams] = useState<{sso?: string, sig?: string} | null>(null);

  // Check for Discourse SSO parameters
  useEffect(() => {
    const { sso, sig, discourse } = router.query;

    // If we have SSO parameters from Discourse, store them
    if (sso && sig && discourse === 'true') {
      console.log('Detected Discourse SSO parameters');
      setDiscourseParams({
        sso: sso as string,
        sig: sig as string
      });
    }
  }, [router.query]);

  // Handle authentication redirect
  useEffect(() => {
    if (isAuthenticated) {
      // If user is authenticated and we have Discourse SSO parameters
      if (discourseParams?.sso && discourseParams?.sig) {
        console.log('User authenticated with Discourse SSO parameters, redirecting...');

        // Dispatch action to process SSO parameters from Discourse (reverse flow)
        dispatch(discourseActions.getDiscourseSSOUrl({
          sso: discourseParams.sso,
          sig: discourseParams.sig
        }));
      } else {
        // Regular authentication redirect
        console.log('User is already authenticated, redirecting...');

        // Get the redirect URL from query parameters or use default
        const redirectUrl = getRedirectUrlAfterAuth('/');

        // Redirect to the target page
        router.push(redirectUrl);
      }
    }
  }, [isAuthenticated, discourseParams, dispatch, router]);

  return <SignInContainer />;
};

export default AuthenticationPage;
