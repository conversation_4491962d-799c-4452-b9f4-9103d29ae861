import { Affiliates } from "@/containers";
import { useRouter } from "next/router";

export default function DynamicCategoryPage() {
  const router = useRouter();
  const { category } = router.query;

  // Extract category slug from URL
  const categorySlug = typeof category === 'string' ? category : '';

  console.log(`Viewing affiliates for category: ${categorySlug}`);
  
  return (
    <Affiliates 
      categoryPath={categorySlug} 
      hideFilterCategories={true} 
    />
  );
}
