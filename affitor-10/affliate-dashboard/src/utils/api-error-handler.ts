import { NextApiResponse } from 'next';

export interface ApiErrorResponse {
  statusCode: number;
  message: string;
  details?: any;
}

/**
 * Formats an error into a standardized API error response
 * @param error The error object
 * @param defaultMessage Default message if none can be extracted
 */
export const formatApiError = (error: any, defaultMessage = 'Internal Server Error'): ApiErrorResponse => {
  console.error('API Error:', error);
  
  // Extract status code from error if available
  const statusCode = error.status || error.statusCode || error.response?.status || 500;
  
  // Extract error message using various possible error formats
  const message = 
    error.message || 
    error.response?.data?.error?.message ||
    error.response?.data?.message ||
    defaultMessage;
  
  return {
    statusCode,
    message,
  };
};

/**
 * Sends a standardized error response
 * @param res Next.js response object
 * @param error The error object
 * @param defaultMessage Default message if none can be extracted
 * @param additionalDetails Optional additional details to include in response
 */
export const sendApiError = (
  res: NextApiResponse, 
  error: any, 
  defaultMessage = 'Internal Server Error',
  additionalDetails?: any
): void => {
  const formattedError = formatApiError(error, defaultMessage);
  
  // Add any additional details if provided
  if (additionalDetails) {
    formattedError.details = additionalDetails;
  }
  
  res.status(formattedError.statusCode).json(formattedError);
};
