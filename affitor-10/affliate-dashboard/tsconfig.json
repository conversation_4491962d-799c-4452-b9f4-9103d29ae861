{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@containers/*": ["./src/containers/*"], "@hooks/*": ["./src/hooks/*"], "@layouts/*": ["./src/layouts/*"], "@pages/*": ["./src/pages/*"], "@styles/*": ["./src/styles/*"], "@utils/*": ["./src/utils/*"], "@features/*": ["./src/features/*"]}, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}