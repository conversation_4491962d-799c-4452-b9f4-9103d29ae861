# ECR Image configuration
ecr_image = "505993055293.dkr.ecr.eu-central-1.amazonaws.com/fight-tv:lastest"
bucket_name    = "fight-tv-ecs-service-app-configuration"

# AWS Region
aws_region = "eu-central-1"

# VPC and Network settings
vpc_cidr = "10.0.0.0/16"
public_subnets = ["10.0.1.0/24", "10.0.2.0/24"]
private_subnets = ["10.0.3.0/24", "10.0.4.0/24"]

# ECS settings
ecs_cluster_name = "fight-tv-prod-cluster"
ecs_service_name = "fight-tv-prod-service"
container_port = 80
desired_count = 1
cpu = 256
memory = 512

# Load balancer settings
lb_name = "fight-tv-prod-lb"
lb_internal = false
lb_listener_port = 80
lb_health_check_path = "/health"
engine_version = 16.6

# Tags
environment = "production"

# More aggressive cost savings (monitor performance closely)
serverless_min_capacity = 0.5  # Reduced minimum (50% savings at idle)
serverless_max_capacity = 3.0  # Reduced maximum (25% savings at peak)

# RDS access
ip_address = "************"