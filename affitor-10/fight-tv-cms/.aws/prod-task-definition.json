{"family": "ecs-task", "containerDefinitions": [{"name": "fight-tv", "image": "505993055293.dkr.ecr.eu-central-1.amazonaws.com/fight-tv:latest", "cpu": 0, "portMappings": [{"name": "port-fargate-1337", "containerPort": 1337, "hostPort": 1337, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::fight-tv-ecs-service-app-configuration/prod.env", "type": "s3"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fight-tv-prod-ecs-cluster-task", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "eu-central-1", "awslogs-stream-prefix": "ecs"}}, "systemControls": []}], "executionRoleArn": "arn:aws:iam::505993055293:role/prod-ecs-task-execution-role", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "3072", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}}