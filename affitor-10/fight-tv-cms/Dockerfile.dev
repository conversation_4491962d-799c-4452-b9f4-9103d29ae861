# Build stage
FROM node:20-alpine3.18 AS build

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ 

# Create yarn cache directory with proper permissions
RUN mkdir -p /usr/local/share/.cache/yarn && chmod 777 /usr/local/share/.cache/yarn

# Copy package files and install all dependencies
COPY package.json yarn.lock ./
RUN yarn install --production=false

# Copy the rest of the app and build
COPY . .
RUN yarn build

# Production stage
FROM node:20-alpine3.18 AS dev

WORKDIR /app

# Copy built app from build stage and set proper ownership
COPY --from=build --chown=node:node /app ./

ENV NODE_ENV=development
ENV PATH=/app/node_modules/.bin:$PATH


# Create cache directories with proper permissions
RUN mkdir -p /home/<USER>/.cache /home/<USER>/.yarn-cache && \
    chown -R node:node /home/<USER>
    chmod -R 755 /home/<USER>

# Switch to non-root user
USER node

EXPOSE 1337

CMD ["yarn", "start"]
