variable "ecr_image" {
  description = "The ECR image URL."
  type        = string
}

variable "environment" {
  description = "The environment name (e.g., dev, stage, prod)."
  type        = string
}

variable "subnets" {
  description = "List of subnet IDs for the ECS service."
  type        = list(string)
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "s3_bucket_name" {
  description = "The name of the S3 bucket."
  type        = string
}

variable "app_config" {
  description = "The JSON configuration for the application."
  type        = map(string)
}

variable "ecs_task_execution_role" {
  description = "The ARN of the ECS task execution role."
  type        = string
}

variable "target_group_arn" {
  description = "The ARN of the target group for the ALB."
  type        = string
}

variable "cluster" {
  description = "The ECS cluster ID."
  type        = string
}

variable "container_port" {
  description = "The port on which the container is listening."
  type        = number
  default     = 1337
}

variable "alb_security_group_id" {
  description = "Security group ID of the ALB"
  type        = string
}

