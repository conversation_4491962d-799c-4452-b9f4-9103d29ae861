locals {
  # Use the current workspace as the environment name
  env              = terraform.workspace
  ssm_password_key = "/${local.env}/rds/master_password"
  rds_identifier   = "${local.env}-aurora-cluster"
}

data "aws_ssm_parameter" "rds_password" {
  name            = local.ssm_password_key
  with_decryption = true
}

resource "aws_rds_cluster" "aurora" {
  cluster_identifier      = "${var.environment}-aurora-cluster"
  engine                  = "aurora-postgresql"
  engine_version          = var.engine_version
  master_username         = var.db_username
  master_password         = data.aws_ssm_parameter.rds_password.value
  database_name           = var.db_name
  db_subnet_group_name    = aws_db_subnet_group.this.name
  vpc_security_group_ids  = [aws_security_group.rds_sg.id]
  backup_retention_period = 5
  preferred_backup_window = "07:00-09:00"
  preferred_maintenance_window = "Mon:00:00-Mon:03:00"
  allow_major_version_upgrade = true

  skip_final_snapshot = true

  serverlessv2_scaling_configuration {
    min_capacity = var.serverless_min_capacity
    max_capacity = var.serverless_max_capacity
  }

  tags = {
    Environment = var.environment
  }
}

# Add at least one instance for the cluster
resource "aws_rds_cluster_instance" "aurora_instance" {
  identifier         = "${var.environment}-aurora-instance"
  cluster_identifier = aws_rds_cluster.aurora.id
  instance_class     = "db.serverless"  # Use serverless instance class
  engine             = aws_rds_cluster.aurora.engine
  engine_version     = aws_rds_cluster.aurora.engine_version

  # Enable auto minor version upgrade
  auto_minor_version_upgrade = true

  # Enable public access
  publicly_accessible = true

  tags = {
    Environment = var.environment
  }
}

# Modified: Use public subnets instead of private subnets
resource "aws_db_subnet_group" "this" {
  name       = "${var.environment}-rds-subnet-group"
  subnet_ids = var.private_subnets

  tags = {
    Environment = var.environment
  }
}

resource "aws_security_group" "rds_sg" {
  name        = "${var.environment}-rds-sg"
  vpc_id      = var.vpc_id
  description = "Allow traffic to Aurora PostgreSQL"

  # Modified: Allow inbound traffic from specific IP
  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["${var.ip_address}/32"]  # Specific IP address
  }

  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [var.ecs_service_sg_id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Environment = var.environment
  }
}