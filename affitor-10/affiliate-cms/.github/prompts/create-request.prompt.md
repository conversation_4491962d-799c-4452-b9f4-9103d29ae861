# Generate a 3rd party request.

Your tasks :

- generate a Client to make a request to 3rd party.
- parse the data from the 3rd response to the structure I need

Ask me for

- The **curl of the 3rd party** I want to create.
- The **sample response from the 3rd party**
- The **the structure data I need to parse**

Example codes:

```
export const YoutubeClient = {
  client: new API({
    baseURL: process.env.YOUTUBE_SEARCH_URL || 'https://youtube-api49.p.rapidapi.com',
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-host': process.env.YOUTUBE_API_HOST || 'youtube-api49.p.rapidapi.com',
      'x-rapidapi-key': process.env.RAPIDAPI_KEY,
    },
  }),

  search: async (keyword: string) => {
    const response: any = await YoutubeClient.client.get('api/search', {
      params: {
        q: keyword,
        maxResults: 20,
        regionCode: 'US',
      },
    });
    return response;
  },

  getVideoInfo: async (videoId: string) => {
    const response: any = await YoutubeClient.client.get('api/video/info', {
      params: {
        videoId,
      },
    });
    return response;
  },
};
```
