{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "stripe_customer_id": {"type": "string"}, "user_tracking_request": {"type": "relation", "relation": "oneToOne", "target": "api::user-tracking-request.user-tracking-request", "mappedBy": "users_permissions_user"}, "subscription_tier": {"type": "relation", "relation": "manyToOne", "target": "api::subscription-tier.subscription-tier", "inversedBy": "users"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "address": {"type": "string"}, "apt": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "zip_code": {"type": "string"}, "state": {"type": "string"}, "paypal_email": {"type": "email"}, "bank_transfer": {"type": "component", "component": "payment.bank-details", "repeatable": false}, "referrer": {"type": "relation", "relation": "oneToOne", "target": "api::referrer.referrer", "mappedBy": "user"}, "referral": {"type": "relation", "relation": "oneToOne", "target": "api::referral.referral", "mappedBy": "user"}, "user_prompts": {"type": "relation", "relation": "oneToMany", "target": "api::user-prompt.user-prompt", "mappedBy": "users_permissions_user"}}}