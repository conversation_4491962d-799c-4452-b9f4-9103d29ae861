/**
 * Referral code middleware
 * Extracts referral code from cookies and adds it to the request context
 */

export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Extract referral code from cookies using multiple methods
    // Try different ways to access the cookie
    // const referralCode = ctx.cookies.get('referral_code');

    // Continue with the request
    await next();
  };
};
