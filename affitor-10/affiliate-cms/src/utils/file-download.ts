import axios from 'axios';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

interface DownloadResult {
  filePath: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
}

/**
 * Download a file from a URL and save it to a temporary location
 * @param url - URL of the file to download
 * @param customFileName - Optional custom filename (without extension)
 * @param downloadDir - Optional download directory (defaults to temp)
 * @returns Promise with download result information
 */
export async function downloadFile(
  url: string,
  customFileName?: string,
  downloadDir?: string
): Promise<DownloadResult> {
  try {
    // Validate URL
    if (!url || typeof url !== 'string') {
      throw new Error('Invalid URL provided');
    }

    // Create download directory if it doesn't exist
    const tempDir = downloadDir || path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    console.log(`Downloading file from: ${url}`);

    // Download the file with proper headers
    const response = await axios({
      method: 'GET',
      url: url,
      responseType: 'stream',
      timeout: 30000, // 30 seconds timeout
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        Accept: 'image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
      },
    });

    // Get content type from response headers
    const contentType = response.headers['content-type'] || 'application/octet-stream';

    // Determine file extension based on content type or URL
    let fileExtension = getFileExtensionFromContentType(contentType);
    if (!fileExtension) {
      fileExtension = getFileExtensionFromUrl(url);
    }
    if (!fileExtension) {
      fileExtension = '.jpg'; // Default to jpg for images
    }

    // Generate filename
    let fileName: string;
    if (customFileName) {
      fileName = `${customFileName}${fileExtension}`;
    } else {
      // Generate unique filename using hash of URL and timestamp
      const hash = crypto
        .createHash('md5')
        .update(url + Date.now())
        .digest('hex')
        .substring(0, 8);
      fileName = `download_${hash}${fileExtension}`;
    }

    // Full file path
    const filePath = path.join(tempDir, fileName);

    // Create write stream and download file
    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);

    // Wait for download to complete
    await new Promise<void>((resolve, reject) => {
      writer.on('finish', () => resolve());
      writer.on('error', reject);
      response.data.on('error', reject);
    });

    // Get file stats
    const stats = fs.statSync(filePath);
    const fileSize = stats.size;

    // Validate that we actually downloaded something
    if (fileSize === 0) {
      fs.unlinkSync(filePath); // Clean up empty file
      throw new Error('Downloaded file is empty');
    }

    console.log(`File downloaded successfully: ${fileName} (${fileSize} bytes)`);

    return {
      filePath,
      fileName,
      fileSize,
      mimeType: contentType,
    };
  } catch (error) {
    console.error('Error downloading file:', error);
    throw error;
  }
}

/**
 * Download an image specifically with additional validation
 * @param imageUrl - URL of the image to download
 * @param customFileName - Optional custom filename (without extension)
 * @param downloadDir - Optional download directory
 * @returns Promise with download result information
 */
export async function downloadImage(
  imageUrl: string,
  customFileName?: string,
  downloadDir?: string
): Promise<DownloadResult> {
  try {
    const result = await downloadFile(imageUrl, customFileName, downloadDir);

    // Validate that it's actually an image
    if (!result.mimeType.startsWith('image/')) {
      // Try to determine if it's an image based on file content
      const buffer = fs.readFileSync(result.filePath);
      const isImage = isImageBuffer(buffer);

      if (!isImage) {
        // Clean up non-image file
        fs.unlinkSync(result.filePath);
        throw new Error('Downloaded file is not a valid image');
      }
    }

    return result;
  } catch (error) {
    console.error('Error downloading image:', error);
    throw error;
  }
}

/**
 * Clean up downloaded files
 * @param filePath - Path to the file to delete
 */
export function cleanupFile(filePath: string): void {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`Cleaned up file: ${filePath}`);
    }
  } catch (error) {
    console.error('Error cleaning up file:', error);
  }
}

/**
 * Get file extension from content type
 * @param contentType - MIME type
 * @returns File extension or null
 */
function getFileExtensionFromContentType(contentType: string): string | null {
  const mimeToExt: { [key: string]: string } = {
    'image/jpeg': '.jpg',
    'image/jpg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'image/bmp': '.bmp',
    'image/svg+xml': '.svg',
    'image/tiff': '.tiff',
    'image/x-icon': '.ico',
  };

  return mimeToExt[contentType.toLowerCase()] || null;
}

/**
 * Get file extension from URL
 * @param url - File URL
 * @returns File extension or null
 */
function getFileExtensionFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const ext = path.extname(pathname).toLowerCase();

    // Validate that it's a known image extension
    const imageExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.bmp',
      '.svg',
      '.tiff',
      '.ico',
    ];
    return imageExtensions.includes(ext) ? ext : null;
  } catch {
    return null;
  }
}

/**
 * Check if buffer contains image data by examining magic bytes
 * @param buffer - File buffer
 * @returns True if buffer appears to be an image
 */
function isImageBuffer(buffer: Buffer): boolean {
  if (buffer.length < 4) return false;

  // Check for common image magic bytes
  const magicBytes = buffer.slice(0, 4);

  // JPEG
  if (magicBytes[0] === 0xff && magicBytes[1] === 0xd8) return true;

  // PNG
  if (
    magicBytes[0] === 0x89 &&
    magicBytes[1] === 0x50 &&
    magicBytes[2] === 0x4e &&
    magicBytes[3] === 0x47
  )
    return true;

  // GIF
  if (buffer.slice(0, 3).toString() === 'GIF') return true;

  // WebP
  if (buffer.slice(8, 12).toString() === 'WEBP') return true;

  // BMP
  if (magicBytes[0] === 0x42 && magicBytes[1] === 0x4d) return true;

  return false;
}
