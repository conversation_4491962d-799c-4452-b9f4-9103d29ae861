{"kind": "collectionType", "collectionName": "referrers", "info": {"singularName": "referrer", "pluralName": "referrers", "displayName": "<PERSON><PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"referral_code": {"type": "string", "unique": true, "required": false}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "inversedBy": "referrer"}, "referrer_status": {"type": "enumeration", "enum": ["active", "blocked"], "default": "active"}, "referrer_links": {"type": "relation", "relation": "oneToMany", "target": "api::referrer-link.referrer-link", "mappedBy": "referrer"}, "referrals": {"type": "relation", "relation": "oneToMany", "target": "api::referral.referral", "mappedBy": "referrer"}, "referral_commissions": {"type": "relation", "relation": "oneToMany", "target": "api::referral-commission.referral-commission", "mappedBy": "referrer"}, "payouts": {"type": "relation", "relation": "oneToMany", "target": "api::payout.payout", "mappedBy": "referrer"}, "balance": {"type": "decimal", "default": 0}, "total_earnings": {"type": "decimal", "default": 0}, "total_revenue": {"type": "decimal", "default": 0}, "custom_commission_percentage": {"type": "decimal", "required": false}, "totalClicks": {"type": "integer", "default": 0, "description": "Total number of clicks across all referrer links"}, "totalLeads": {"type": "integer", "default": 0, "description": "Total number of leads generated across all referrer links"}, "totalConversions": {"type": "integer", "default": 0, "description": "Total number of conversions across all referrer links"}}}