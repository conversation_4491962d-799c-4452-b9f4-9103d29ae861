{"kind": "collectionType", "collectionName": "payouts", "info": {"singularName": "payout", "pluralName": "payouts", "displayName": "Payout", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"referrer": {"type": "relation", "relation": "manyToOne", "target": "api::referrer.referrer", "inversedBy": "payouts"}, "payout_status": {"type": "enumeration", "enum": ["pending", "approved", "completed"]}, "method": {"type": "enumeration", "enum": ["paypal", "bank transfer"]}, "amount": {"type": "decimal"}, "processing_fee": {"type": "decimal"}, "payout_date": {"type": "datetime"}}}