export interface IAirtableSource {
  fields: {
    'Submitter Name': string;
    'Submitter Email': string;
    'Product Name': string;
    Description: string;
    Category: string[];
    'Commission Rate': string;
    'Review Status': string;
    [key: string]: any; // For any additional fields that might exist
  };
  id?: string; // Airtable records typically have an id
  createdTime?: string; // And a creation timestamp
}
