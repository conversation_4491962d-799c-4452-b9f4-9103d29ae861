{"kind": "collectionType", "collectionName": "user_prompts", "info": {"singularName": "user-prompt", "pluralName": "user-prompts", "displayName": "User Prompt", "description": "User-created prompts for AI interactions"}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string", "required": true, "minLength": 3, "maxLength": 100}, "content": {"type": "text", "required": true, "minLength": 10, "maxLength": 5000}, "description": {"type": "text", "maxLength": 500}, "tags": {"type": "json", "default": []}, "isFavorite": {"type": "boolean", "default": false}, "usageCount": {"type": "integer", "default": 0}, "lastUsedAt": {"type": "datetime"}, "users_permissions_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "user_prompts"}}}