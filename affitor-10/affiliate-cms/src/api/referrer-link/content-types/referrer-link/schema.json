{"kind": "collectionType", "collectionName": "referrer_links", "info": {"singularName": "referrer-link", "pluralName": "referrer-links", "displayName": "Referrer Links", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string"}, "url": {"type": "string"}, "visitors": {"type": "integer"}, "leads": {"type": "integer"}, "conversions": {"type": "integer"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "referrer": {"type": "relation", "relation": "manyToOne", "target": "api::referrer.referrer", "inversedBy": "referrer_links"}, "track_links": {"type": "relation", "relation": "oneToMany", "target": "api::track-link.track-link", "mappedBy": "referrer_link"}, "referrals": {"type": "relation", "relation": "oneToMany", "target": "api::referral.referral", "mappedBy": "referrer_link"}, "short_link": {"type": "string", "unique": true}, "page": {"type": "relation", "relation": "oneToOne", "target": "api::page.page", "inversedBy": "referrer_link"}, "direct_page_views": {"type": "integer", "default": 0, "description": "Views from direct page URLs"}, "referrer_link_views": {"type": "integer", "default": 0, "description": "Views from referrer-link URLs"}, "short_link_views": {"type": "integer", "default": 0, "description": "Views from short-link URLs"}, "referrer_sources": {"type": "json", "description": "JSON object storing referrer information and counts"}}}