export interface ISocial {
  title: string;
  description: string;
  published_from: string;
  channel_id: string;
  channel_title: string;
  channel_avatar?: string;
  thumbnail: string;
  likes?: number;
  comments?: number;
  link: string;
  views: number;
  shares?: number;
  type: string;
  platform: string;
  keyword?: string;
  transcript?: string;
  is_verified?: boolean;
  is_displayed?: boolean;
  is_from_crawler?: boolean;
}

export interface IXPost extends ISocial {
  photos: string[];
  x_id: string;
}

export interface IRedditPost extends ISocial {
  post_id: string;
}
export interface IVideo extends ISocial {
  video_id: string;
  duration: string;
  video_link: string;
}
