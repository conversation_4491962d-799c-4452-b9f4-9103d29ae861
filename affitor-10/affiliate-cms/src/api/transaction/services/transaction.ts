/**
 * transaction service
 */

import { factories } from '@strapi/strapi';
import { ITransaction } from '../../user-tracking-request/interfaces';

export default factories.createCoreService('api::transaction.transaction', ({ strapi }) => ({
  // Get user transaction history
  async getUserTransactions(userId: number) {
    return await strapi.entityService.findMany('api::transaction.transaction', {
      filters: {
        user: {
          id: userId,
        },
      },
      sort: { transaction_date: 'desc' },
      populate: {
        subscription_tier: {
          filters: {
            publishedAt: { $ne: null }, // Only get published subscription tiers
          },
        },
      },
    });
  },

  // Create a pending transaction for Stripe checkout
  async createPendingTransaction(
    userId: number,
    tierId: number,
    paymentMethod: 'stripe' | 'free',
    paymentDetails?: any
  ) {
    try {
      const tier = await strapi.entityService.findOne(
        'api::subscription-tier.subscription-tier',
        tierId
      );

      if (!tier) {
        throw new Error(`Subscription tier with ID ${tierId} not found`);
      }

      return await strapi.entityService.create('api::transaction.transaction', {
        data: {
          user: userId,
          subscription_tier: tier.id,
          amount: tier.price,
          currency: 'USD',
          payment_status: 'pending', // Start as pending
          payment_method: paymentMethod,
          transaction_date: new Date(),
          payment_details: paymentDetails || {},
          publishedAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Error creating pending transaction:', error);
      throw error;
    }
  },

  // Process Stripe payment result
  async processStripePayment(sessionId: string, success: boolean): Promise<any> {
    // Find transaction by Stripe session ID
    const transactions = await strapi.entityService.findMany('api::transaction.transaction', {
      filters: {
        payment_details: {
          $containsi: sessionId,
        },
      },
      populate: ['user', 'subscription_tier'],
    });

    if (!transactions || transactions.length === 0) {
      throw new Error(`No transaction found for Stripe session ID: ${sessionId}`);
    }

    const transaction: any = transactions[0];
    const status = success ? 'completed' : 'failed';

    // Update transaction status
    const updatedTransaction: any = await strapi.entityService.update(
      'api::transaction.transaction',
      transaction.id,
      {
        data: {
          payment_status: status,
          payment_details: {
            ...transaction.payment_details,
            payment_completed: success,
            processed_at: new Date(),
          },
        },
      }
    );

    // If payment successful, update user subscription
    if (status === 'completed' && transaction.user && transaction.subscription_tier) {
      const userTrackingService = strapi.service(
        'api::user-tracking-request.user-tracking-request'
      );

      // Get the tier information first to access its properties
      const tier = await strapi.entityService.findOne(
        'api::subscription-tier.subscription-tier',
        transaction.subscription_tier.id
      );

      await userTrackingService.updateSubscription(
        transaction.user.id,
        transaction.subscription_tier.id,
        tier ? tier.request_limit : undefined
      );
    }

    return updatedTransaction;
  },

  // Generate an invoice for a transaction
  async generateInvoice(transactionId: number) {
    const transaction: any = await strapi.entityService.findOne(
      'api::transaction.transaction',
      transactionId,
      {
        populate: {
          user: true,
          subscription_tier: {
            filters: {
              publishedAt: { $ne: null }, // Only get published subscription tiers
            },
          },
        },
      }
    );

    if (!transaction) {
      throw new Error(`Transaction with ID ${transactionId} not found`);
    }

    // Generate invoice ID if not exists
    if (!transaction.invoice_id) {
      const invoiceId = `INV-${Date.now()}-${transactionId}`;

      await strapi.entityService.update('api::transaction.transaction', transactionId, {
        data: {
          stripe_invoice_id: invoiceId,
        },
      });

      transaction.stripe_invoice_id = invoiceId;
    }

    // In a real app, you might generate a PDF or HTML invoice here
    return {
      stripe_invoice_id: transaction.stripe_invoice_id,
      customer_name: transaction.user.username,
      customer_email: transaction.user.email,
      amount: transaction.amount,
      currency: transaction.currency,
      date: transaction.transaction_date,
      subscription: transaction.subscription_tier.display_name,
      status: transaction.status,
    };
  },
}));
