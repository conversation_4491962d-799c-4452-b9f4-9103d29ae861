{"kind": "collectionType", "collectionName": "track_links", "info": {"singularName": "track-link", "pluralName": "track-links", "displayName": "Track Link", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"referrer_url": {"type": "string"}, "type": {"type": "enumeration", "enum": ["visitors", "leads", "conversions"]}, "referrer_link": {"type": "relation", "relation": "manyToOne", "target": "api::referrer-link.referrer-link", "inversedBy": "track_links"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}