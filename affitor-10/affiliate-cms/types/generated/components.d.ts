import type { Schema, Struct } from '@strapi/strapi';

export interface PaymentBankDetails extends Struct.ComponentSchema {
  collectionName: 'components_payment_bank_details';
  info: {
    description: 'Bank transfer payment information';
    displayName: 'Bank Details';
  };
  attributes: {
    account_number: Schema.Attribute.String;
    address: Schema.Attribute.String;
    business_name: Schema.Attribute.String;
    city: Schema.Attribute.String;
    country: Schema.Attribute.String;
    first_name: Schema.Attribute.String;
    last_name: Schema.Attribute.String;
    state: Schema.Attribute.String;
    swift_code: Schema.Attribute.String;
    zip_code: Schema.Attribute.String;
  };
}

export interface SharedAirtableConfig extends Struct.ComponentSchema {
  collectionName: 'components_shared_airtable_configs';
  info: {
    description: '';
    displayName: 'Airtable Config';
    icon: 'apps';
  };
  attributes: {
    airtable_id: Schema.Attribute.String & Schema.Attribute.Unique;
    user_ref_link: Schema.Attribute.String;
    user_ref_rate: Schema.Attribute.Decimal;
  };
}

export interface SharedCommissionLevel extends Struct.ComponentSchema {
  collectionName: 'components_shared_commission_levels';
  info: {
    description: '';
    displayName: 'Key - Value';
    icon: 'bulletList';
  };
  attributes: {
    description: Schema.Attribute.String;
    value: Schema.Attribute.Decimal;
  };
}

export interface SharedListing extends Struct.ComponentSchema {
  collectionName: 'components_shared_listings';
  info: {
    description: '';
    displayName: 'listing';
  };
  attributes: {
    item: Schema.Attribute.String;
  };
}

export interface SharedMedia extends Struct.ComponentSchema {
  collectionName: 'components_shared_media';
  info: {
    displayName: 'Media';
    icon: 'file-video';
  };
  attributes: {
    file: Schema.Attribute.Media<'images' | 'files' | 'videos'>;
  };
}

export interface SharedPricingRange extends Struct.ComponentSchema {
  collectionName: 'components_shared_pricing_ranges';
  info: {
    description: '';
    displayName: 'range';
  };
  attributes: {
    from: Schema.Attribute.Decimal;
    to: Schema.Attribute.Decimal;
  };
}

export interface SharedQuote extends Struct.ComponentSchema {
  collectionName: 'components_shared_quotes';
  info: {
    displayName: 'Quote';
    icon: 'indent';
  };
  attributes: {
    body: Schema.Attribute.Text;
    title: Schema.Attribute.String;
  };
}

export interface SharedRichText extends Struct.ComponentSchema {
  collectionName: 'components_shared_rich_texts';
  info: {
    description: '';
    displayName: 'Rich text';
    icon: 'align-justify';
  };
  attributes: {
    body: Schema.Attribute.RichText;
  };
}

export interface SharedSeo extends Struct.ComponentSchema {
  collectionName: 'components_shared_seos';
  info: {
    description: '';
    displayName: 'Seo';
    icon: 'allergies';
    name: 'Seo';
  };
  attributes: {
    metaDescription: Schema.Attribute.Text & Schema.Attribute.Required;
    metaTitle: Schema.Attribute.String & Schema.Attribute.Required;
    shareImage: Schema.Attribute.Media<'images'>;
  };
}

export interface SharedSlider extends Struct.ComponentSchema {
  collectionName: 'components_shared_sliders';
  info: {
    description: '';
    displayName: 'Slider';
    icon: 'address-book';
  };
  attributes: {
    files: Schema.Attribute.Media<'images', true>;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'payment.bank-details': PaymentBankDetails;
      'shared.airtable-config': SharedAirtableConfig;
      'shared.commission-level': SharedCommissionLevel;
      'shared.listing': SharedListing;
      'shared.media': SharedMedia;
      'shared.pricing-range': SharedPricingRange;
      'shared.quote': SharedQuote;
      'shared.rich-text': SharedRichText;
      'shared.seo': SharedSeo;
      'shared.slider': SharedSlider;
    }
  }
}
